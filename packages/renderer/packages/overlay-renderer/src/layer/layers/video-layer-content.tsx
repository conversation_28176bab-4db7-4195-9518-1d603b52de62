import { continueRender, delayRender, OffthreadVideo, useCurrentFrame } from 'remotion'
import { VideoOverlay } from '@clipnest/remotion-shared/types'
import React, { useEffect } from 'react'
import { animationTemplates } from '@clipnest/remotion-shared/constants'
import { toAbsoluteUrl } from '@clipnest/remotion-shared/utils'
import { useRenderContext } from '../../render.context'

interface VideoLayerContentProps {
  overlay: VideoOverlay
  baseUrl?: string
}

export const VideoLayerContent: React.FC<VideoLayerContentProps> = ({
  overlay,
  baseUrl,
}) => {
  const frame = useCurrentFrame()
  const { playerMetadata: { fps } } = useRenderContext()

  // Determine the video source URL
  let videoSrc = overlay.src

  // If it's a relative URL and baseUrl is provided, use baseUrl
  if (overlay.src.startsWith('/') && baseUrl) {
    videoSrc = `${baseUrl}${overlay.src}`
  }
  // Otherwise use the toAbsoluteUrl helper for relative URLs
  else if (overlay.src.startsWith('/')) {
    videoSrc = toAbsoluteUrl(overlay.src)
  }

  useEffect(() => {
    // console.debug(`Preparing to load video: ${overlay.src}`)
    const handle = delayRender('Loading video')

    // Create a video element to preload the video
    const video = document.createElement('video')
    video.src = videoSrc

    const handleLoadedMetadata = () => {
      // console.debug(`Video metadata loaded: ${overlay.src}`)
      continueRender(handle)
    }

    const handleError = (_: ErrorEvent) => {
      // console.error(`Error loading video ${overlay.src}:`, error)
      continueRender(handle)
    }

    video.addEventListener('loadedmetadata', handleLoadedMetadata)
    video.addEventListener('error', handleError)

    return () => {
      video.removeEventListener('loadedmetadata', handleLoadedMetadata)
      video.removeEventListener('error', handleError)
      // Ensure we don't leave hanging render delays
      continueRender(handle)
    }
  }, [overlay.src])

  // 计算淡入淡出的帧数
  const fadeInFrames = overlay.fadeInDuration ? Math.round(overlay.fadeInDuration * fps) : 0
  const fadeOutFrames = overlay.fadeOutDuration ? Math.round(overlay.fadeOutDuration * fps) : 0

  // 计算淡入淡出的不透明度
  let fadeInOpacity = 1
  let fadeOutOpacity = 1

  // 淡入效果：在开始的 fadeInFrames 帧内，不透明度从 0 逐渐增加到 1
  if (fadeInFrames > 0 && frame < fadeInFrames) {
    fadeInOpacity = frame / fadeInFrames
  }

  // 淡出效果：在结束的 fadeOutFrames 帧内，不透明度从 1 逐渐减少到 0
  if (fadeOutFrames > 0 && frame >= overlay.durationInFrames - fadeOutFrames) {
    fadeOutOpacity = (overlay.durationInFrames - frame) / fadeOutFrames
  }

  // 将淡入和淡出的不透明度相乘，得到最终的不透明度
  // 这样即使淡入和淡出时间重叠，也能得到正确的效果
  let fadeOpacity = fadeInOpacity * fadeOutOpacity

  // 确保不透明度在 0-1 范围内
  fadeOpacity = Math.max(0, Math.min(1, fadeOpacity))

  // Calculate if we're in the exit phase (last 30 frames)
  const isExitPhase = frame >= overlay.durationInFrames - fps

  // Apply enter animation only during entry phase
  const enterAnimation
    = !isExitPhase && overlay.styles.animation?.enter
      ? animationTemplates[overlay.styles.animation.enter]?.enter(
        frame,
        overlay.durationInFrames,
      )
      : {}

  // Apply exit animation only during exit phase
  const exitAnimation
    = isExitPhase && overlay.styles.animation?.exit
      ? animationTemplates[overlay.styles.animation.exit]?.exit(
        frame,
        overlay.durationInFrames,
      )
      : {}

  const videoStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    objectFit: overlay.styles.objectFit || 'cover',
    // 将基础不透明度与淡入淡出不透明度相乘，确保两者都生效
    opacity: (overlay.styles.opacity ?? 1) * fadeOpacity,
    transform: overlay.styles.transform || 'none',
    borderRadius: overlay.styles.borderRadius || '0px',
    filter: overlay.styles.filter || 'none',
    boxShadow: overlay.styles.boxShadow || 'none',
    border: overlay.styles.border || 'none',
    // 应用动画效果
    ...(isExitPhase ? exitAnimation : enterAnimation),
    // 添加平滑过渡效果
    transition: 'opacity 0.1s ease-in-out',
  }

  // Create a container style that includes padding and background color
  const containerStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    padding: overlay.styles.padding || '0px',
    backgroundColor: overlay.styles.paddingBackgroundColor || 'transparent',
    display: 'flex', // Use flexbox for centering
    alignItems: 'center',
    justifyContent: 'center',
  }

  // 计算视频的实际开始时间，考虑裁剪片头
  const actualVideoStartTime = (overlay.videoStartTime || 0) + (overlay.trimStart || 0)

  // 计算视频的实际结束时间，考虑裁剪片尾
  const trimEndFrames = overlay.trimEnd ? Math.round(overlay.trimEnd * fps) : 0
  const actualDurationInFrames = overlay.durationInFrames - trimEndFrames

  // 如果当前帧超过了裁剪后的时长，不显示视频
  if (frame >= actualDurationInFrames) {
    return <div style={containerStyle} />
  }

  return (
    <div style={containerStyle}>
      <OffthreadVideo
        src={videoSrc}
        startFrom={actualVideoStartTime}
        style={videoStyle}
        volume={overlay.styles.volume ?? 1}
        playbackRate={overlay.speed ?? 1}
      />
    </div>
  )
}
