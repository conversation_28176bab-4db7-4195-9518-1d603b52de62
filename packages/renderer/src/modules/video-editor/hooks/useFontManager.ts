import { useCallback } from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types'
import { useResource } from './resource/useResource'
import * as opentype from 'opentype.js'
import { cacheManager } from '@/libs/cache/cache-manager'
import { useResourceLoadingStore } from './resource/useResourceLoadingStore'
import { TextToSvgConvertor } from '@clipnest/overlay-renderer'

const LOCAL_PREFIX = ['/fonts/', './fonts/']

const LOG_PREFIX = '[字体管理]'

/**
 * 判断是否是本地字体文件（public 目录中的字体）
 */
function isLocalFont(src: string): boolean {
  return LOCAL_PREFIX.some(prefix => src.startsWith(prefix))
}

/**
 * 构建字体文件URL
 */
async function buildFontUrl(fontPath: string, downloadResourceToCache: any, version?: string): Promise<string> {
  if (isLocalFont(fontPath)) {
    return fontPath
  }

  // 处理远程字体文件
  let localPath = cacheManager.resource.getResourcePathSync(ResourceType.FONT, fontPath)

  if (!localPath) {
    localPath = await downloadResourceToCache({
      url: fontPath,
      resourceType: ResourceType.FONT,
      version: version ?? '1.0.0'
    })
  }

  if (!localPath) {
    throw new Error(`无法获取字体文件: ${fontPath}`)
  }

  return localPath.startsWith('http') ? localPath : `file://${localPath.replace(/\\/g, '/')}`
}

/**
 * 加载字体到 DOM
 */
async function loadFontToDOM(fontName: string, fontUrl: string): Promise<boolean> {
  try {
    const fontFace = new FontFace(fontName, `url("${fontUrl}")`)
    await fontFace.load()
    document.fonts.add(fontFace)
    return true
  } catch (error) {
    console.error(`${LOG_PREFIX} DOM 字体加载失败: ${fontName}`, error)
    throw error
  }
}

export function useFontManager() {
  const { downloadResourceToCache } = useResource()
  const { startResourceLoading, endResourceLoading, isResourceLoading } = useResourceLoadingStore()

  /**
   * 检查字体是否已在 DOM 中加载
   * 使用 useMemo 缓存 document.fonts 的转换结果以提升性能
   */
  const isFontStyleLoaded = useCallback((fontPath: string) => {
    // 优化：避免每次都遍历整个 document.fonts
    const loadedFonts = Array.from(document.fonts).filter(font => font.status === 'loaded')
    return loadedFonts.some(font =>
      font.family === fontPath || (font as any).src?.includes(fontPath)
    )
  }, [])

  /**
   * 加载字体并返回 opentype.Font 对象
   */
  const loadFontWithOpentype = useCallback(
    async (fontPath: string, fontName: string): Promise<opentype.Font> => {
    // 首先尝试从缓存获取
      const cachedFont = cacheManager.font.getFont(fontPath)
      if (cachedFont) {
        return cachedFont
      }

      // 检查是否正在加载
      // if (isResourceLoading(fontPath, ResourceType.FONT)) {
      //   console.log('Resource is Loading')
      //   return null
      // }

      // 开始加载状态
      startResourceLoading(fontPath, ResourceType.FONT)

      try {
        // 加载字体到缓存
        const font = await cacheManager.font.cacheFont(fontPath)

        if (font) {
          // 尝试同时加载到 DOM（失败不影响主要功能）
          try {
            const fontUrl = await buildFontUrl(fontPath, downloadResourceToCache)
            await loadFontToDOM(fontName, fontUrl)
          } catch (domError) {
            console.warn(`${LOG_PREFIX} DOM 字体加载失败，但 opentype.js 加载成功:`, domError)
          }
        }

        return font
      } catch (error) {
        console.error(`${LOG_PREFIX} 加载失败: ${fontName}`, error)
        throw error
      } finally {
      // 确保总是结束加载状态
        endResourceLoading(fontPath, ResourceType.FONT)
      }
    }, [startResourceLoading, endResourceLoading, isResourceLoading, downloadResourceToCache])

  /**
   * 计算文字尺寸的便捷方法
   */
  const calculateTextSize = useCallback(async (
    text: string = '默认文字',
    fontSize: number = 50,
    fontPath: string,
    fontName: string
  ): Promise<{ width: number; height: number } | null> => {
    try {
      // 加载字体并获取 opentype.Font 对象
      const font = await loadFontWithOpentype(fontPath, fontName)
      if (!font) {
        console.warn('[文字尺寸计算] 字体加载失败')
        return null
      }

      // 创建 TextToSvgConvertor 实例
      const convertor = new TextToSvgConvertor(font, font)

      // 计算尺寸
      const width = convertor._getWidth(text, fontSize)
      const height = convertor._getHeight(fontSize)

      return { width, height }
    } catch (error) {
      console.error('[文字尺寸计算] 计算失败:', error)
      return null
    }
  }, [loadFontWithOpentype])

  /**
   * 加载字体到 DOM（向后兼容）
   */
  const loadFont = useCallback(async (fontPath: string, fontName: string): Promise<boolean> => {
    // 检查是否正在加载
    if (isResourceLoading(fontPath, ResourceType.FONT)) {
      console.log(`${LOG_PREFIX} 字体正在加载中: ${fontName}`)
      return false
    }

    // 开始加载状态
    startResourceLoading(fontPath, ResourceType.FONT)

    try {
      const fontUrl = await buildFontUrl(fontPath, downloadResourceToCache)

      if (isLocalFont(fontPath)) {
        console.log(`${LOG_PREFIX} 加载本地字体到 DOM: ${fontPath}`)
      }

      return await loadFontToDOM(fontName, fontUrl)
    } catch (error) {
      console.error(`${LOG_PREFIX} 加载失败: ${fontName}`, error)
      return false
    } finally {
      // 结束加载状态
      endResourceLoading(fontPath, ResourceType.FONT)
    }
  }, [downloadResourceToCache, startResourceLoading, endResourceLoading, isResourceLoading])

  return {
    isFontStyleLoaded,
    loadFont,
    calculateTextSize,
  }
}
