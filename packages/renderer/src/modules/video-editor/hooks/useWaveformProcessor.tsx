import { useState, useEffect } from 'react'
import { FPS } from '@/modules/video-editor/constants'

// 定义与共享类型相同的接口，避免路径导入问题
interface WaveformData {
  peaks: number[]
  length: number
}

interface WaveformOptions {
  numPoints?: number
  startTime?: number
  duration?: number
  fps?: number
}

/**
 * A React hook that processes audio files to generate waveform visualization data.
 * The hook fetches an audio file, analyzes it, and generates an array of normalized amplitude values
 * that can be used to render a waveform visualization.
 *
 * @param src - URL of the audio file to process
 * @param startFromSound - Start time offset in frames (default: 0)
 * @param durationInFrames - Duration to process in frames
 * @param options - Configuration options
 * @param options.numPoints - Number of data points to generate for the waveform (default: 400)
 * @param options.fps - Frames per second for time calculations (default: 30)
 *
 * @returns Object containing:
 *   - peaks: Array of normalized amplitude values between 0 and 1
 *   - length: Total number of samples processed
 *
 * @example
 * ```tsx
 * const waveform = useWaveformProcessor(
 *   'path/to/audio.mp3',
 *   0,
 *   300, // 10 seconds at 30fps
 *   { numPoints: 200, fps: 30 }
 * );
 * ```
 */
export function useWaveformProcessor(
  src: string | undefined,
  startFromSound: number = 0,
  durationInFrames: number,
  options: WaveformOptions = {},
): WaveformData | null {
  // 使用浏览器版本的波形处理器（作为后备）
  const [browserWaveformData, setBrowserWaveformData] = useState<WaveformData | null>(null)

  useEffect(() => {
    if (!src) return

    let isActive = true

    // TODO: 迁移实现到 Main Process
    const processAudio = async () => {
      try {
        const response = await fetch(src)
        const arrayBuffer = await response.arrayBuffer()
        const audioContext = new AudioContext()
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer)

        if (!isActive) return

        const sampleRate = audioBuffer.sampleRate
        const channelData = audioBuffer.getChannelData(0)
        const startTime = startFromSound / (options.fps || FPS)
        const duration = durationInFrames / (options.fps || FPS)

        const startSample = Math.floor(startTime * sampleRate)
        const samplesForDuration = Math.floor(duration * sampleRate)
        const samplesPerPeak = Math.floor(samplesForDuration / (options.numPoints || 400))

        const peaks = Array.from({ length: options.numPoints || 400 }, (_, i) => {
          const start = startSample + i * samplesPerPeak
          const end = Math.min(start + samplesPerPeak, channelData.length)

          let peakMax = 0
          let sumSquares = 0
          let validSamples = 0

          for (let j = start; j < end; j++) {
            if (j >= channelData.length) break
            const value = Math.abs(channelData[j])
            peakMax = Math.max(peakMax, value)
            sumSquares += value * value
            validSamples++
          }

          if (validSamples === 0) return 0
          const rms = Math.sqrt(sumSquares / validSamples)
          return (peakMax + rms) / 2
        })

        // Normalize using 95th percentile
        const sortedPeaks = [...peaks].sort((a, b) => a - b)
        const normalizeValue = sortedPeaks[Math.floor(peaks.length * 0.95)] || 1
        const normalizedPeaks = peaks.map(peak =>
          Math.min(peak / normalizeValue, 1),
        )

        if (!isActive) return

        setBrowserWaveformData({
          peaks: normalizedPeaks,
          length: samplesForDuration,
        })
      }
      catch (error) {
        console.error('Error processing audio waveform in browser:', error)
      }
    }

    processAudio()
    return () => {
      isActive = false
    }
  }, [src, startFromSound, durationInFrames, options.fps, options.numPoints])

  // 优先使用Electron版本的结果，如果不可用则使用浏览器版本
  return browserWaveformData
}
