import { useCallback } from 'react'
import { useEditorContext, useTimeline } from '@/modules/video-editor/contexts'
import { calculateRenderableOverlays } from '@/modules/video-editor/utils/overlay-helper'
import { TrackType } from '@/modules/video-editor/types'
import { RenderableOverlay } from '@clipnest/remotion-shared/types'

/**
 * 从原始的轨道中筛选出实际需要渲染的视频
 */
export const useCalculateRenderableOverlays = () => {
  const { tracks } = useEditorContext()
  const { videoActivation, narrationActivation } = useTimeline()

  return useCallback(
    (): RenderableOverlay[] => calculateRenderableOverlays(tracks, {
      [TrackType.VIDEO]: videoActivation,
      [TrackType.NARRATION]: narrationActivation
    }),
    [tracks, videoActivation, narrationActivation]
  )
}
