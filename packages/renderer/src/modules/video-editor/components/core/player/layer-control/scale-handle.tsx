import React, { useMemo } from 'react'
import { useCurrentScale } from 'remotion'
import { Overlay, OverlayType } from '@clipnest/remotion-shared/types'
import { useDraggable } from '@dnd-kit/core'
import { LAYER_CONTROL_DRAG_ACTIONS } from './constants'

const HANDLE_SIZE = 8

type ScaleHandleProps = {

  type: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'

  overlay: Overlay
}

export const ScaleHandle: React.FC<ScaleHandleProps> = ({ type, overlay }) => {
  const scale = useCurrentScale()
  const size = useMemo(() => Math.round(HANDLE_SIZE / scale), [scale])
  const borderSize = useMemo(() => 1 / scale, [scale])

  const sizeStyle = useMemo((): React.CSSProperties => ({
    position: 'absolute',
    height: Number.isFinite(size) ? size : HANDLE_SIZE,
    width: Number.isFinite(size) ? size : HANDLE_SIZE,
    backgroundColor: 'white',
    border: `${borderSize}px solid #3B8BF2`,
    zIndex: 9999,
    pointerEvents: 'all'
  }), [borderSize, size])

  const style: React.CSSProperties = useMemo(
    () => {
      const margin = -size / 2 - borderSize

      if (type === 'top-left') {
        return {
          ...sizeStyle,
          marginLeft: margin,
          marginTop: margin,
          cursor: 'nwse-resize',
        }
      }

      if (type === 'top-right') {
        return {
          ...sizeStyle,
          marginTop: margin,
          marginRight: margin,
          right: 0,
          cursor: 'nesw-resize',
        }
      }

      if (type === 'bottom-left') {
        return {
          ...sizeStyle,
          marginBottom: margin,
          marginLeft: margin,
          bottom: 0,
          cursor: 'nesw-resize',
        }
      }

      if (type === 'bottom-right') {
        return {
          ...sizeStyle,
          marginBottom: margin,
          marginRight: margin,
          right: 0,
          bottom: 0,
          cursor: 'nwse-resize',
        }
      }

      throw new Error('Unknown type: ' + JSON.stringify(type))
    },
    [sizeStyle, type]
  )

  const { setNodeRef, listeners, attributes } = useDraggable({
    id: `layer-scale-${type}-${overlay.id}`,
    data: {
      action: LAYER_CONTROL_DRAG_ACTIONS.scale,
      overlay
    }
  })

  if (overlay.type === OverlayType.SOUND) {
    return null
  }

  return (
    <div
      ref={setNodeRef}
      {...listeners}
      {...attributes}
      style={style}
    />
  )
}
