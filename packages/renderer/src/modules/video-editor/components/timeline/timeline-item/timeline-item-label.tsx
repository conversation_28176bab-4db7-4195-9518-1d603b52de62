import React, { useMemo } from 'react'
import { Camera, Film, Sticker, Type, Volume2 } from 'lucide-react'
import { Overlay, OverlayType } from '@clipnest/remotion-shared/types'
import { ENABLE_VIDEO_KEYFRAME_EXTRACTING } from '../../../constants'
import clsx from 'clsx'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { findStoryboardIndex } from '@/modules/video-editor/utils/overlay-helper'

/**
 * Props for the TimelineItemLabel component
 * @interface TimelineItemLabelProps
 * @property {Overlay} item - The overlay item to display (text, video, image, sound, or caption)
 */
interface TimelineItemLabelProps {
  item: Overlay
}

/**
 * Component that renders a label for a timeline item, showing its type and content
 * with appropriate styling and icons based on the overlay type.
 */
export const TimelineItemLabel: React.FC<TimelineItemLabelProps> = ({ item }) => {
  const { tracks } = useEditorContext()

  /**
   * Returns the appropriate icon component based on the overlay type
   * @param {string} type - The type of overlay
   */
  const getItemIcon = (type: string) => {
    switch (type) {
      case OverlayType.TEXT:
        return <Type className="w-2 h-2 mr-0.5" />
      case OverlayType.VIDEO:
        return <Film className="w-2 h-2 mr-0.5" />
      case OverlayType.SOUND:
        return <Volume2 className="w-2 h-2 mr-0.5" />
      case OverlayType.STICKER:
        return <Sticker className="w-2 h-2 mr-0.5" />
      case OverlayType.STORYBOARD:
        return <Camera className="w-2 h-2 mr-0.5" />
      case OverlayType.CAPTION:
        return <></>
      default:
        return null
    }
  }

  /**
   * Determines the label content to display based on the item type and properties
   * - For captions: returns empty string
   * - For text: returns the content string
   * - For media (image/video/sound): returns filename from src or name property
   * - Fallback: returns the item type
   *
   * @returns The label content to display
   */
  const labelContent = useMemo(
    () => {
      if (item.type === OverlayType.STORYBOARD) {
        return `分镜${findStoryboardIndex(tracks, item) + 1}`
      }

      if (item.type === OverlayType.CAPTION) {
        return ''
      }

      if (item.type === OverlayType.TEXT && typeof item.content === 'string') {
        return item.content
      }

      if ('src' in item && item.src) {
        const filename = item.src.split('/').pop() || ''
        return filename.split('?')[0]
      }

      if ('name' in item && item.name) {
        return String(item.name)
      }

      return String(item.type)
    },
    [item, tracks]
  )

  return (
    <div
      style={{ maxWidth: 'calc(100% - 16px)' }}
      className="absolute inset-0 flex items-center z-20 pl-2"
    >
      {item.type !== OverlayType.CAPTION
        && (item.type !== OverlayType.VIDEO || !ENABLE_VIDEO_KEYFRAME_EXTRACTING)
        && (
          <div
            className={clsx(
              `flex items-center text-[8px] rounded-[2px] py-0.5 px-1
               transition-all duration-200 ease-in-out max-w-full`,
              item.type === OverlayType.TEXT
                ? 'bg-purple-200/30 text-white dark:bg-purple-200/30 dark:text-white'
                : item.type === OverlayType.STICKER
                  ? 'bg-pink-200/30 text-white dark:bg-pink-200/30 dark:text-white'
                  : item.type === OverlayType.SOUND
                    ? 'bg-amber-200/80 text-gray-500 dark:bg-amber-200/80 dark:text-gray-500'
                    : item.type === OverlayType.VIDEO
                      ? 'bg-purple-200/30 text-white dark:bg-purple-200/30 dark:text-white'
                      : 'bg-gray-100/80 text-gray-900 dark:bg-gray-700/90 dark:text-gray-200'
            )}
          >
            <div className="flex items-center gap-0.5 w-full">
              {getItemIcon(item.type)}
              <span className="capitalize truncate max-w-[100px]">
                {labelContent}
              </span>
            </div>
          </div>
        )}
    </div>
  )
}
