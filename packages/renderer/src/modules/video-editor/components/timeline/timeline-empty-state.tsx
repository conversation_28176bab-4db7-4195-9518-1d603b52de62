/**
 * TimelineEmptyState Component
 *
 * 当时间轴为空状态时显示的遮罩层，提供快速开始选项
 */

import React, { useState } from 'react'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { Button } from '@/components/ui/button'
import { FileText, Sparkles } from 'lucide-react'
import { EVENT_NAMES, useEventEmitter } from '@/modules/video-editor/hooks/useEventBus'

export const TimelineEmptyState: React.FC = () => {
  const { tracks } = useEditorContext()
  const [isManuallyHidden, setIsManuallyHidden] = useState(false)
  const emitEvent = useEventEmitter()

  const isEmpty = tracks.every(track => track.overlays.length === 0)

  if (!isEmpty || isManuallyHidden) {
    return null
  }

  const handleStartCreating = () => {
    setIsManuallyHidden(true)
  }

  const handleImportFromScript = () => {
    emitEvent(EVENT_NAMES.HIGHLIGHT_SCRIPT_BUTTONS)
  }

  return (
    <div className="absolute inset-0 bg-white/80 dark:bg-gray-900/80 backdrop-blur-sm flex items-center justify-center z-40">
      <div className="flex flex-col items-center gap-6 p-8 max-w-md mx-auto text-center">
        {/* 标题 */}
        <div className="space-y-2">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
            开始创作您的视频
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            选择一种方式开始您的创作之旅
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 w-full">
          <Button
            onClick={handleImportFromScript}
            className="flex-1 h-12 bg-gradient-brand hover:opacity-90 text-white font-medium transition-opacity"
            size="lg"
          >
            <FileText className="w-4 h-4 mr-2" />
            从脚本一键导入
          </Button>

          <Button
            onClick={handleStartCreating}
            variant="outline"
            className="flex-1 h-12 border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800 font-medium"
            size="lg"
          >
            <Sparkles className="w-4 h-4 mr-2" />
            开始自由创作
          </Button>
        </div>

        <p className="text-xs text-gray-500 dark:text-gray-500 max-w-xs">
          您也可以直接拖拽素材到时间轴上开始创作
        </p>
      </div>
    </div>
  )
}
