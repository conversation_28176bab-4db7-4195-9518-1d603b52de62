import React, { FC, useCallback } from 'react'
import { Overlay, OverlayType } from '@clipnest/remotion-shared/types'
import { TextSetting } from '@/modules/video-editor/components/overlays/text/text-setting'
import { SoundSetting } from '@/modules/video-editor/components/overlays/sounds/sound-setting'
import { VideoSetting } from '@/modules/video-editor/components/overlays/video/video-setting'
import { CaptionSetting } from '@/modules/video-editor/components/overlays/captions/caption-setting'
import { ImageSetting } from '@/modules/video-editor/components/overlays/images/image-setting'
import {
  OverlayEditingContext,
  OverlayEditingContextValues,
  useCachedOverlaysContext,
  useEditorContext
} from '@/modules/video-editor/contexts'
import { merge } from 'lodash'
import { SingleOverlayUpdatePayload } from '@/modules/video-editor/contexts/editor/useOverlays'

export const OverlayEditingPanel: React.FC<{ localOverlay: Overlay }> = ({ localOverlay }) => {
  const { updateOverlay } = useEditorContext()
  const { requestUpdate } = useCachedOverlaysContext()

  const handleUpdateOverlay = useCallback<OverlayEditingContextValues['requestUpdate']>(
    (updater, commit = false) => {
      if (!localOverlay) return

      const updatedOverlay = (typeof updater === 'function') && localOverlay
        ? updater(localOverlay as any)
        : merge({}, localOverlay, updater) as SingleOverlayUpdatePayload

      requestUpdate(localOverlay.id, updatedOverlay)

      if (commit) {
        updateOverlay(localOverlay.id, () => updatedOverlay)
      }
    },
    [localOverlay]
  )

  const overlayTypeComponentMap: Partial<Record<OverlayType, FC>> = {
    [OverlayType.TEXT]: TextSetting,
    [OverlayType.SOUND]: SoundSetting,
    [OverlayType.VIDEO]: VideoSetting,
    [OverlayType.CAPTION]: CaptionSetting,
    [OverlayType.STICKER]: ImageSetting,
  }

  const Component = overlayTypeComponentMap[localOverlay.type]

  return (
    <div className="p-4 h-full overflow-y-auto">
      <OverlayEditingContext
        value={{
          localOverlay,
          requestUpdate: handleUpdateOverlay
        }}
      >
        {Component && <Component />}
      </OverlayEditingContext>
    </div>
  )
}
