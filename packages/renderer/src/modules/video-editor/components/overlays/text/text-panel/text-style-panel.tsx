import React, { use<PERSON><PERSON><PERSON>, useMemo, useState } from 'react'
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'
import { Toggle } from '@/components/ui/toggle'
import { AlignCenter, AlignLeft, AlignRight, Bold, CheckIcon, Italic, Underline } from 'lucide-react'
import { FormNumberInput, FormSlider } from '../../common/form-components'

import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { FontResource } from '@/types/resources'
import { FontPreviewItem } from '../font-preview-item'
import { useInfiniteQueryFontUnified } from '@/hooks/queries/useQueryFont'
import InfiniteResourceList from '@/components/InfiniteResourceList'
import { SettingsTabs, TabItem } from '../../../shared/settings-tabs'
import { OverlayType, TextOverlay } from '@clipnest/remotion-shared/types'
import { useOverlayEditing } from '@/modules/video-editor/contexts'
import { TextPropOverrides, useCalculateTextRenderInfo } from '@clipnest/overlay-renderer'
import { useQuery } from '@tanstack/react-query'
import { cacheManager } from '@/libs/cache/cache-manager'
import { ColorPicker } from '@/components/color-picker'
import { ResourceType } from '@app/shared/types/resource-cache.types'

const useHandleTextChange = (fontSrc: string) => {
  const { data: font = null } = useQuery({
    queryKey: ['useHandleTextStyleChange', fontSrc],
    queryFn: () => cacheManager.font.getFont(fontSrc)
  })

  const { localOverlay, requestUpdate } = useOverlayEditing<TextOverlay>()

  const { buildCalcTextRenderInfoFunction } = useCalculateTextRenderInfo(
    font,
    localOverlay.type === OverlayType.TEXT ? localOverlay : null
  )

  return useCallback(
    (update: TextPropOverrides, commit?: boolean) => {
      const calculateTextRenderInfo = buildCalcTextRenderInfoFunction()

      // 计算新的渲染信息，传入样式覆盖参数
      const maybeRenderInfo = calculateTextRenderInfo?.(update)

      const { width: originalWidth } = localOverlay
      const { minHeight, minWidth = 0 } = maybeRenderInfo || {}

      const { content, fontSize, lineSpacing, letterSpacing } = update

      requestUpdate({
        content,
        width: Math.max(originalWidth, minWidth),
        height: minHeight,
        styles: {
          fontSize,
          lineSpacing,
          letterSpacing
        }
      }, commit)
    },
    [buildCalcTextRenderInfoFunction, localOverlay, requestUpdate]
  )
}

export const TextStylePanel: React.FC = () => {
  const [isSelectOpen, setIsSelectOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string>('list')

  const { localOverlay: textOverlay, requestUpdate: updateOverlay } = useOverlayEditing<TextOverlay>()

  const fontInfiniteQueryResult = useInfiniteQueryFontUnified({
    pageNo: 1,
    pageSize: 20,
    selectedCategory: selectedCategory === 'list' ? undefined : selectedCategory
  })

  // 从查询结果中查找当前字体
  const currentFont = useMemo(() => {
    const allFonts = fontInfiniteQueryResult.data?.pages.flatMap(page => page.list) || []
    return allFonts.find(font => font.title === textOverlay.styles?.fontFamily)
  }, [fontInfiniteQueryResult, textOverlay])

  const handleTextStyleChange = useHandleTextChange(textOverlay.src)

  // 样式更新函数
  const handleStyleChange = useCallback(
    (styleUpdates: Partial<TextOverlay['styles']>) => {
      updateOverlay<OverlayType.TEXT>({ styles: styleUpdates }, true)
    },
    [updateOverlay]
  )

  const handleFontChange = useCallback(
    async (font: FontResource.Font) => {
      if (!font) return
      const fontSrc = font.content.url

      const update: Record<string, any> = {
        src: fontSrc
      }

      try {
        update.styles = { fontFamily: font.title }

        await cacheManager.resource.cacheResource(ResourceType.FONT, fontSrc)
        await cacheManager.font.cacheFont(fontSrc)

        update.styles = { fontFamily: font.title }
        update.localSrc = await cacheManager.font.getLocalUrl(fontSrc)

        setIsSelectOpen(false)
        updateOverlay(update, true)
      } catch (error) {
        console.error(`[字体面板] 异步加载字体失败: ${font.title}`, error)
      }
    },
    [updateOverlay]
  )

  const selectedFontFamily = useMemo(() => textOverlay.styles.fontFamily, [textOverlay.styles.fontFamily])

  const renderFontItem = useCallback((font: FontResource.Font) => (
    <FontPreviewItem
      key={font.id}
      font={font}
      isSelected={font.title === selectedFontFamily}
      onFontChange={handleFontChange}
    />
  ), [selectedFontFamily, handleFontChange])

  const tabs: TabItem[] = useMemo(
    () => [
      {
        value: 'list',
        label: '字体列表',
        content: (
          <div className="h-[300px] overflow-y-auto">
            <div className="p-1 space-y-1">
              <InfiniteResourceList
                queryResult={fontInfiniteQueryResult}
                renderItem={renderFontItem}
                emptyText="暂无字体"
                loadingText="加载字体中..."
              />
            </div>
          </div>
        )
      },
      {
        value: 'collected',
        label: '收藏字体',
        content: (
          <div className="h-[300px] overflow-y-auto">
            <div className="p-1 space-y-1">
              <InfiniteResourceList
                queryResult={fontInfiniteQueryResult}
                renderItem={renderFontItem}
                emptyText="暂无收藏字体"
                loadingText="加载收藏字体中..."
              />
            </div>
          </div>
        )
      }
    ],
    [fontInfiniteQueryResult, renderFontItem]
  )

  return (
    <div className="space-y-6">
      {/* ChromePicker 黑夜模式样式 */}
      <div className="relative w-full overflow-hidden rounded-b-sm border border-border bg-muted/40">
        <textarea
          value={textOverlay.content || ''}
          onChange={e => handleTextStyleChange({ content: e.target.value })}
          onBlur={() => handleTextStyleChange({}, true)}
          placeholder="Enter your text here..."
          className="w-full min-h-[60px] bg-transparent p-2 text-foreground placeholder:text-muted-foreground outline-none focus:outline-none"
          spellCheck="false"
        />
      </div>

      {/*Typography Settings */}
      <div className="space-y-4 rounded-md bg-background/50 p-4 border">
        <h3 className="text-sm font-medium">排版</h3>

        <div className="flex flex-col gap-2">
          <label className="text-xs text-muted-foreground">字体</label>
          <Popover open={isSelectOpen} onOpenChange={setIsSelectOpen}>
            <PopoverTrigger asChild>
              <div
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 cursor-pointer"
              >
                <div className="flex items-center justify-between w-full">
                  <span style={currentFont ? { fontFamily: currentFont.title } : {}}>
                    {textOverlay.styles.fontFamily || 'Select a font'}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {currentFont?.title}
                  </span>
                </div>
              </div>
            </PopoverTrigger>
            <PopoverContent className="w-[300px] p-0" align="start">
              <SettingsTabs
                tabs={tabs}
                defaultTab="list"
                onTabChange={value => setSelectedCategory(value)}
              />
            </PopoverContent>
          </Popover>
        </div>

        <div className="grid grid-cols-2">
          {/* 对齐 */}
          <div className="flex flex-col gap-2">
            <label className="text-xs text-muted-foreground">对齐</label>
            {/* "center" | "end" | "start" | "left" | "right" */}
            <ToggleGroup
              type="single"
              className="justify-start gap-1"
              value={textOverlay.styles.textAlign}
              onValueChange={value => {
                if (value) updateOverlay({ styles: { textAlign: value as any } }, true)
              }}
            >
              <ToggleGroupItem
                value="left"
                aria-label="Align left"
                className="h-10 w-10"
              >
                <AlignLeft className="h-4 w-4" />
              </ToggleGroupItem>
              <ToggleGroupItem
                value="center"
                aria-label="Align center"
                className="h-10 w-10"
              >
                <AlignCenter className="h-4 w-4" />
              </ToggleGroupItem>
              <ToggleGroupItem
                value="right"
                aria-label="Align right"
                className="h-10 w-10"
              >
                <AlignRight className="h-4 w-4" />
              </ToggleGroupItem>
            </ToggleGroup>
          </div>

          {/* 文字样式 */}
          <div className="flex flex-col gap-2">
            <label className="text-xs text-muted-foreground">文字样式</label>
            <div className="flex gap-1">
              <Toggle
                pressed={textOverlay.styles.fontWeight === 'bold'}
                aria-label="Bold"
                className="h-10 w-10"
                onPressedChange={pressed => {
                  updateOverlay({ styles: { fontWeight: pressed ? 'bold' : 'normal' } }, true)
                }}
              >
                <Bold className="h-4 w-4" />
              </Toggle>
              <Toggle
                pressed={textOverlay.styles.fontStyle === 'italic'}
                aria-label="Italic"
                className="h-10 w-10"
                onPressedChange={pressed => {
                  updateOverlay({ styles: { fontStyle: pressed ? 'italic' : 'normal' } }, true)
                }}
              >
                <Italic className="h-4 w-4" />
              </Toggle>
              <Toggle
                pressed={textOverlay.styles.underlineEnabled}
                aria-label="Underline"
                className="h-10 w-10"
                onPressedChange={pressed => {
                  updateOverlay({ styles: { underlineEnabled: pressed } }, true)
                }}
              >
                <Underline className="h-4 w-4" />
              </Toggle>
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-2">
          <label className="text-xs text-muted-foreground">字号 (px)</label>
          <FormSlider
            showInput
            value={textOverlay.styles.fontSize || 150}
            min={50}
            max={500}
            step={10}
            onChange={(fontSize, commit) => handleTextStyleChange({ fontSize }, commit)}
          />
        </div>

        {/* Letter Spacing */}
        <div className="flex flex-col gap-2">
          <label className="text-xs text-muted-foreground">字间距 (px)</label>
          <FormSlider
            value={textOverlay.styles.letterSpacing || 0}
            min={-5}
            max={100}
            step={1}
            showInput
            onChange={(letterSpacing, commit) => handleTextStyleChange({ letterSpacing }, commit)}
          />
        </div>

        {/* Line Height */}
        <div className="flex flex-col gap-2">
          <label className="text-xs text-muted-foreground">行间距 (倍数)</label>
          <FormSlider
            value={textOverlay.styles.lineSpacing || 0}
            min={0}
            max={2}
            step={0.05}
            showInput
            onChange={(lineSpacing, commit) => handleTextStyleChange({ lineSpacing }, commit)}
          />
        </div>
      </div>

      {/* 描边设置 */}
      <div className="space-y-4 rounded-md bg-background/50 p-4 border">
        <h3 className="text-sm font-medium">描边</h3>

        {/* 描边开关 */}
        <div className="flex items-center justify-between">
          <label className="text-xs text-muted-foreground">启用描边</label>
          <Toggle
            pressed={textOverlay.styles.strokeEnabled || false}
            onPressedChange={pressed => handleStyleChange({ strokeEnabled: pressed })}
            aria-label="Enable Stroke"
          >
            <CheckIcon />
          </Toggle>
        </div>

        {textOverlay.styles.strokeEnabled && (
          <>
            {/* 描边粗细 */}
            <div className="flex flex-col gap-2">
              <label className="text-xs text-muted-foreground">描边粗细</label>

              <FormSlider
                value={textOverlay.styles.strokeWidth || 1}
                onChange={(value, commit) => updateOverlay({ styles: { strokeWidth: value } }, commit)}
                min={1}
                max={80}
                step={1}
                showInput={false}
              />
            </div>

            {/* 描边颜色 */}
            <div className="flex flex-col gap-2">
              <label className="text-xs text-muted-foreground">描边颜色</label>
              <ColorPicker
                color={textOverlay.styles.strokeColor || '#000000'}
                onChange={({ hex }) => updateOverlay({ styles: { strokeColor: hex } })}
                onChangeComplete={({ hex }) => updateOverlay({ styles: { strokeColor: hex } }, true)}
              />
            </div>
          </>
        )}
      </div>

      {/* 阴影设置 */}
      <div className="space-y-4 rounded-md bg-background/50 p-4 border">
        <h3 className="text-sm font-medium">阴影</h3>

        {/* 阴影开关 */}
        <div className="flex items-center justify-between">
          <label className="text-xs text-muted-foreground">启用阴影</label>
          <Toggle
            pressed={textOverlay.styles.shadowEnabled || false}
            onPressedChange={pressed => updateOverlay({ styles: { shadowEnabled: pressed } }, true)}
            aria-label="Enable Shadow"
          >
            <CheckIcon />
          </Toggle>
        </div>

        {textOverlay.styles.shadowEnabled && (
          <>
            {/* 阴影距离和角度 */}
            <div className="grid grid-cols-2 gap-4">
              <div className="flex flex-col gap-2">
                <label className="text-xs text-muted-foreground">阴影距离</label>
                <FormNumberInput
                  value={textOverlay.styles.shadowDistance || 5}
                  onChange={value => updateOverlay({ styles: { shadowDistance: value } }, true)}
                  min={0}
                  max={50}
                  step={1}
                  suffix="px"
                />
              </div>
              <div className="flex flex-col gap-2">
                <label className="text-xs text-muted-foreground">阴影角度</label>
                <FormNumberInput
                  value={textOverlay.styles.shadowAngle || 45}
                  onChange={value => updateOverlay({ styles: { shadowAngle: value } }, true)}
                  min={0}
                  max={360}
                  step={15}
                  suffix="°"
                />
              </div>
            </div>

            {/* 阴影模糊度和透明度 */}
            <div className="grid grid-cols-2 gap-4">
              <div className="flex flex-col gap-2">
                <label className="text-xs text-muted-foreground">模糊度</label>
                <FormNumberInput
                  value={textOverlay.styles.shadowBlur || 1}
                  onChange={value => updateOverlay({ styles: { shadowBlur: value } }, true)}
                  min={0}
                  max={50}
                  step={1}
                  suffix="px"
                />
              </div>
              <div className="flex flex-col gap-2">
                <label className="text-xs text-muted-foreground">透明度</label>
                <FormSlider
                  value={textOverlay.styles.shadowOpacity || 1}
                  onChange={(value, commit) => updateOverlay({ styles: { shadowOpacity: value } }, commit)}
                  min={0}
                  max={1}
                  step={0.1}
                  showInput={false}
                />
              </div>
            </div>

            {/* 阴影颜色 */}
            <div className="flex flex-col gap-2">
              <label className="text-xs text-muted-foreground">阴影颜色</label>
              <ColorPicker
                color={textOverlay.styles.shadowColor || '#000000'}
                onChange={({ hex }) => updateOverlay({ styles: { shadowColor: hex } })}
                onChangeComplete={({ hex }) => updateOverlay({ styles: { shadowColor: hex } }, true)}
              />
            </div>
          </>
        )}
      </div>

      {/* Colors */}
      <div className="space-y-4 rounded-md bg-background/50 p-4 border">
        <h3 className="text-sm font-medium">颜色</h3>

        <div className="grid grid-cols-3 gap-4">

          <div className="flex flex-col gap-2">
            <label className="text-xs text-muted-foreground">
              文字颜色
            </label>
            <ColorPicker
              color={textOverlay.styles.color || '#000000'}
              onChange={({ hex }) => updateOverlay({ styles: { color: hex } })}
              onChangeComplete={({ hex }) => updateOverlay({ styles: { color: hex } }, true)}
            />
          </div>

          {/* 文字透明度 */}
          <div className="flex flex-col gap-2">
            <label className="text-xs text-muted-foreground">文字透明度</label>
            <FormSlider
              value={textOverlay.styles.textOpacity || 1}
              onChange={(value, commit) => updateOverlay({ styles: { textOpacity: value } }, commit)}
              min={0}
              max={1}
              step={0.1}
              showInput={false}
            />
          </div>

          <div className="flex flex-col gap-2">
            <label className="text-xs text-muted-foreground">
              背景色
            </label>
            <ColorPicker
              color={textOverlay.styles.backgroundColor || '#000000'}
              onChange={({ hex }) => updateOverlay({ styles: { backgroundColor: hex } })}
              onChangeComplete={({ hex }) => updateOverlay({ styles: { backgroundColor: hex } }, true)}
            />
          </div>

        </div>
      </div>

      {/* Debug information */}
      <div className="mt-4 p-3 bg-muted/50 rounded text-xs space-y-1">
        <div className="font-semibold text-foreground mb-2">样式调试信息</div>
      </div>
    </div>
  )
}
