import React from 'react'
import { SettingsTabs, TabItem } from '../../shared/settings-tabs'
import { TextSettingsPanel } from './text-settings-panel'
import { TextStylePanel } from './text-panel/text-style-panel'
import { BubbleTextPanel } from './text-panel/bubble-text-panel'
import { StyledTextPanel } from './text-panel/styled-text-panel'

export const TextSetting: React.FC = () => {
  const baseTabs: TabItem[] = [
    {
      value: 'basic',
      label: '基础',
      content: (
        <TextStylePanel />
      )
    },
    {
      value: 'bubble',
      label: '气泡字',
      content: (
        <BubbleTextPanel />
      )
    },
    {
      value: 'animation',
      label: '动画',
      content: (
        <TextSettingsPanel />
      )
    },
    {
      value: 'styled',
      label: '花体字',
      content: (
        <StyledTextPanel />
      )
    },
  ]

  return (
    <div className="space-y-3">
      <SettingsTabs tabs={baseTabs} defaultTab="basic" />
    </div>

  )
}
