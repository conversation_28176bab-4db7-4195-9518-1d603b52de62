import React, { useCallback, useState } from 'react'
import { useInfiniteQueryBubbleText } from '@/hooks/queries/useQueryFont'
import { FontResource } from '@/types/resources'
import { getImageDimensions } from '../../../../utils/image'
import { TextOverlay } from '@clipnest/remotion-shared/types'
import { useEditorContext, useOverlayEditing } from '@/modules/video-editor/contexts'

// 气泡图项组件
interface BubbleItemProps {
  bubbleResource: FontResource.BubbleLetters
  isLoading: boolean
  onApplyToCurrentOverlay: () => void
}

export const BubbleTextPanel: React.FC = () => {
  const [loadingBubbles, setLoadingBubbles] = useState<Set<number>>(new Set())
  const {  getAspectRatioDimensions } = useEditorContext()
  const { localOverlay: textOverlay, requestUpdate: updateOverlay } = useOverlayEditing<TextOverlay>()

  const fontInfiniteQueryResult = useInfiniteQueryBubbleText({
    pageNo: 1,
    pageSize: 20,
  })

  // 解析气泡图文字位置信息
  const parseBubbleTextRect = (contentString?: string): [number, number, number, number] | null => {
    if (!contentString) return null

    try {
      const parsed = JSON.parse(contentString)
      if (parsed.textRect && Array.isArray(parsed.textRect) && parsed.textRect.length === 4) {
        return parsed.textRect as [number, number, number, number]
      }
    } catch (error) {
      console.warn('Failed to parse bubble text rect:', error)
    }

    return null
  }

  // 计算基于当前文字的气泡图尺寸
  const calculateBubbleSizeBasedOnText = (
    bubbleDimensions: { width: number; height: number },
    _textRect?: [number, number, number, number] | null
  ) => {
    const { width: canvasWidth, height: canvasHeight } = getAspectRatioDimensions()

    // 计算当前文字的实际渲染字体大小
    // 使用当前 overlay 的尺寸来计算基准字体大小
    const currentFontSize = calculateSmartFontSize(
      textOverlay.width,
      textOverlay.height,
      textOverlay.content,
      canvasWidth,
      canvasHeight
    )

    console.log('当前字体大小计算:', {
      overlaySize: { width: textOverlay.width, height: textOverlay.height },
      content: textOverlay.content,
      calculatedFontSize: currentFontSize
    })

    // 估算文字的实际渲染宽度
    // 这是一个简化的估算，基于字符数量和字体大小
    const avgCharWidth = currentFontSize * 0.6 // 经验值，中英文混合的平均宽度
    const textLines = textOverlay.content.split('\n')
    const maxLineLength = Math.max(...textLines.map(line => line.length))
    const estimatedTextWidth = maxLineLength * avgCharWidth

    // 考虑行高和多行文本
    const lineHeight = currentFontSize * 1.2 // 默认行高
    const estimatedTextHeight = textLines.length * lineHeight

    // 简化尺寸计算逻辑
    // 基于当前文字宽度，按气泡图比例计算合适的尺寸
    const bubbleAspectRatio = bubbleDimensions.width / bubbleDimensions.height

    // 让气泡图的宽度是文字宽度的1.5-2倍，确保有足够空间
    let targetWidth = estimatedTextWidth * 1.8
    let targetHeight = targetWidth / bubbleAspectRatio

    // 如果高度不够容纳文字，则基于高度重新计算
    if (targetHeight < estimatedTextHeight * 1.5) {
      targetHeight = estimatedTextHeight * 1.5
      targetWidth = targetHeight * bubbleAspectRatio
    }

    console.log('气泡图尺寸计算:', {
      estimatedText: { width: estimatedTextWidth, height: estimatedTextHeight },
      bubbleAspectRatio,
      target: { width: targetWidth, height: targetHeight }
    })

    // 确保不超出画布边界
    const maxWidth = canvasWidth * 0.8
    const maxHeight = canvasHeight * 0.8

    let finalWidth = targetWidth
    let finalHeight = targetHeight

    if (finalWidth > maxWidth || finalHeight > maxHeight) {
      const scaleX = maxWidth / finalWidth
      const scaleY = maxHeight / finalHeight
      const scale = Math.min(scaleX, scaleY)

      finalWidth = Math.round(finalWidth * scale)
      finalHeight = Math.round(finalHeight * scale)
    }

    // 确保最小尺寸
    const minSize = 100
    finalWidth = Math.max(minSize, finalWidth)
    finalHeight = Math.max(minSize, finalHeight)

    return {
      width: Math.round(finalWidth),
      height: Math.round(finalHeight)
    }
  }

  // 应用气泡图到文字覆盖层
  const applyBubbleToOverlay = useCallback(async (bubbleResource: FontResource.BubbleLetters) => {
    const bubbleId = bubbleResource.id
    setLoadingBubbles(prev => new Set(prev).add(bubbleId))

    try {
      const { content } = bubbleResource

      if (!content.itemUrl) {
        console.warn('Bubble resource missing itemUrl')
        return
      }

      // 获取气泡图尺寸
      const dimensions = await getImageDimensions(content.itemUrl)
      if (!dimensions) {
        console.warn('Failed to get bubble image dimensions')
        return
      }

      // 解析文字位置信息
      const textRect = parseBubbleTextRect(content.content)

      // 基于当前文字尺寸计算气泡图的合适尺寸
      const { width: overlayWidth, height: overlayHeight } = calculateBubbleSizeBasedOnText(
        dimensions,
        textRect
      )

      // 更新当前overlay的样式和尺寸
      updateOverlay( {
        width: overlayWidth,
        height: overlayHeight,
        styles: {
          ...textOverlay.styles,
          backgroundImage: content.itemUrl,
          bubbleTextRect: textRect || undefined,
          backgroundColor: 'transparent',
        }
      }, true)
    } catch (error) {
      console.error('Error applying bubble to overlay:', error)
    } finally {
      setLoadingBubbles(prev => {
        const newSet = new Set(prev)
        newSet.delete(bubbleId)
        return newSet
      })
    }
  }, [textOverlay.id, textOverlay.styles, updateOverlay])

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-sm font-medium">气泡文字</h3>

        {/* 气泡图列表 */}
        <div className="grid grid-cols-3 gap-3">
          {fontInfiniteQueryResult.data?.pages.map(page =>
            page.list.map(bubbleResource => (
              <BubbleItem
                key={bubbleResource.id}
                bubbleResource={bubbleResource}
                isLoading={loadingBubbles.has(bubbleResource.id)}
                onApplyToCurrentOverlay={() => applyBubbleToOverlay(bubbleResource)}
              />
            ))
          )}
        </div>

        {/* 加载更多按钮 */}
        {fontInfiniteQueryResult.hasNextPage && (
          <button
            onClick={() => fontInfiniteQueryResult.fetchNextPage()}
            disabled={fontInfiniteQueryResult.isFetchingNextPage}
            className="w-full py-2 text-sm text-muted-foreground hover:text-foreground transition-colors"
          >
            {fontInfiniteQueryResult.isFetchingNextPage ? '加载中...' : '加载更多'}
          </button>
        )}
      </div>
    </div>
  )
}

/**
 * 估算文本在给定宽度下的行数（考虑自动换行）
 * @param text - 文本内容
 * @param fontSize - 字体大小（像素）
 * @param containerWidth - 容器宽度
 * @param _fontFamily - 字体族
 * @returns 估算的行数
 */
function estimateTextLines(
  text: string,
  fontSize: number,
  containerWidth: number,
  _fontFamily: string = 'Arial'
): number {
  // 估算字符平均宽度（基于字体大小）
  // 中文字符通常比英文字符宽，这里使用一个平均值
  const avgCharWidth = fontSize * 0.6 // 经验值，可能需要根据实际情况调整

  const lines = text.split('\n')
  let totalLines = 0

  for (const line of lines) {
    if (line.length === 0) {
      totalLines += 1 // 空行
      continue
    }

    // 估算这一行需要多少行来显示
    const lineWidth = line.length * avgCharWidth
    const linesNeeded = Math.ceil(lineWidth / (containerWidth * 0.9)) // 0.9 考虑 padding
    totalLines += Math.max(1, linesNeeded)
  }

  return totalLines
}

/**
 * 基于画幅大小计算基础字体大小
 * 字体大小占画幅宽度的1/4，高度的1/4
 * @param canvasWidth - 画布宽度
 * @param canvasHeight - 画布高度
 * @returns 基础字体大小（像素）
 */
function calculateBaseFontSize(
  canvasWidth: number,
  canvasHeight: number
): number {
  // 基于画幅大小计算基础字体大小
  // 取宽度的1/4和高度的1/4中的较小值
  const widthBasedSize = canvasWidth / 4
  const heightBasedSize = canvasHeight / 4

  const baseSize = Math.min(widthBasedSize, heightBasedSize)

  // 设置合理的范围
  const minSize = 12
  const maxSize = 200

  return Math.max(minSize, Math.min(baseSize, maxSize))
}

/**
 * 计算智能字体大小（基于画幅大小，不再使用 fontScale）
 * @param containerWidth - 容器宽度
 * @param containerHeight - 容器高度
 * @param content - 文本内容
 * @param canvasWidth - 画布宽度
 * @param canvasHeight - 画布高度
 * @returns 计算后的字体大小（像素）
 */
function calculateSmartFontSize(
  containerWidth: number,
  containerHeight: number,
  content: string,
  canvasWidth: number,
  canvasHeight: number
): number {
  // 获取基于画幅的基础字体大小
  const baseFontSize = calculateBaseFontSize(canvasWidth, canvasHeight)

  // 确保容器不超出画布边界
  const boundedWidth = Math.min(containerWidth, canvasWidth)
  const boundedHeight = Math.min(containerHeight, canvasHeight)

  // 设置字体大小范围，基于基础字体大小
  const minSize = Math.max(12, baseFontSize * 0.3) // 最小为基础大小的30%
  const maxSize = Math.min(baseFontSize * 2, boundedHeight * 0.8) // 最大为基础大小的2倍或容器高度的80%

  // 使用二分查找找到最适合的字体大小
  let low = minSize
  let high = maxSize
  let bestFontSize = baseFontSize // 默认使用基础字体大小

  // 二分查找最大可用字体大小
  while (low <= high) {
    const mid = Math.floor((low + high) / 2)
    const estimatedLines = estimateTextLines(content, mid, boundedWidth)
    const requiredHeight = estimatedLines * mid * 1.2 // 1.2 是行高系数

    if (requiredHeight <= boundedHeight * 0.9) { // 0.9 考虑 padding
      bestFontSize = mid
      low = mid + 1
    } else {
      high = mid - 1
    }
  }

  // 确保在合理范围内
  return Math.max(minSize, Math.min(bestFontSize, maxSize))
}

const BubbleItem: React.FC<BubbleItemProps> = ({
  bubbleResource,
  isLoading,
  onApplyToCurrentOverlay,
}) => {
  const [imageError, setImageError] = useState(false)

  const { cover, content, title } = bubbleResource
  const previewUrl = cover?.url || content.coverUrl

  return (
    <div className="group relative">
      {/* 预览图 */}
      <div
        className="aspect-square bg-muted/10 rounded-lg overflow-hidden cursor-pointer hover:ring-2 hover:ring-primary transition-all"
        onClick={onApplyToCurrentOverlay}
      >
        {previewUrl && !imageError ? (
          <img
            src={previewUrl}
            alt={title}
            className="w-full h-full object-cover"
            onError={() => setImageError(true)}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-muted-foreground">
            <span className="text-xs">气泡</span>
          </div>
        )}

        {/* 加载状态 */}
        {isLoading && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
          </div>
        )}
      </div>

      {/* 操作按钮 */}
      <div className="absolute inset-x-0 bottom-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
        <div className="p-2">
          <button
            onClick={e => {
              e.stopPropagation()
              onApplyToCurrentOverlay()
            }}
            disabled={isLoading}
            className="w-full px-2 py-1 text-xs text-white bg-white/20 hover:bg-white/30 rounded transition-colors"
          >
            应用气泡图
          </button>
        </div>
      </div>
    </div>
  )
}
