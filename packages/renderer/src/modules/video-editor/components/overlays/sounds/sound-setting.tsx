import React, { useEffect, useRef, useState } from 'react'
import { SoundOverlay } from '@clipnest/remotion-shared/types'
import { Pause, Play } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { FormSlider, SectionTitle } from '../common/form-components'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { AudioFadeControls } from '../common/fade-controls'
import { MediaControls } from '../common/media-controls'
import { useOverlayEditing } from '@/modules/video-editor/contexts'

export const SoundSetting: React.FC = () => {
  const { localOverlay, requestUpdate: updateOverlay } = useOverlayEditing<SoundOverlay>()

  const [isPlaying, setIsPlaying] = useState(false)
  const audioRef = useRef<HTMLAudioElement | null>(null)

  useEffect(() => {
    audioRef.current = new Audio(localOverlay.src)
    return () => {
      if (audioRef.current) {
        audioRef.current.pause()
        audioRef.current.currentTime = 0
      }
    }
  }, [localOverlay.src])

  const togglePlay = () => {
    if (!audioRef.current) return

    if (isPlaying) {
      audioRef.current.pause()
    }
    else {
      audioRef.current
        .play()
        .catch(error => console.error('Error playing audio:', error))
    }
    setIsPlaying(!isPlaying)
  }

  return (
    <div className="space-y-4">
      {/* Sound Info with Play Button */}
      <div className="flex items-center gap-3 p-4  rounded-md border">
        <Button
          variant="ghost"
          size="sm"
          onClick={togglePlay}
          className="h-8 w-8 rounded-full bg-transparent hover:bg-accent text-foreground"
        >
          {isPlaying
            ? (
              <Pause className="h-4 w-4" />
            )
            : (
              <Play className="h-4 w-4" />
            )}
        </Button>
        <div className="min-w-0 flex-1">
          <p className="text-sm font-medium text-foreground truncate">
            {localOverlay.content}
          </p>
        </div>
      </div>

      {/* 音量 */}
      <div className="space-y-3 rounded-md  p-3 border border-border">
        <SectionTitle title="音量" />
        <button
          onClick={() =>
            updateOverlay({ styles: {
              volume: localOverlay?.styles?.volume === 0 ? 1 : 0,
            } }, true)}
          className={`text-xs px-2.5 py-1.5 rounded-md transition-colors ${
            (localOverlay?.styles?.volume ?? 1) === 0
              ? 'bg-primary/20 text-primary hover:bg-primary/30'
              : ' text-muted-foreground hover:bg-muted/20'
          }`}
        >
          {(localOverlay?.styles?.volume ?? 1) === 0 ? 'Unmute' : 'Mute'}
        </button>
        <div className="flex w-full items-center gap-2">
          <FormSlider
            value={localOverlay?.styles?.volume ?? 1}
            onChange={(val, commit) => updateOverlay({ styles: { volume: val } }, commit)}
            max={1}
            min={0}
            step={0.1}
            showInput={false}
          />
          <span className="text-xs text-muted-foreground min-w-[40px] text-right">
            {Math.round((localOverlay?.styles?.volume ?? 1) * 100)}
            %
          </span>
        </div>
        <div className="gap-6 flex items-center mt-2">
          <div className="flex items-center gap-2">
            <Checkbox id="gain" />
            <Label htmlFor="gain">音频增益</Label>
          </div>
          <div className="flex items-center gap-2">
            <Checkbox id="reduction" />
            <Label htmlFor="reduction">音频降噪</Label>
          </div>
        </div>
      </div>

      {/* 淡入淡出控制 */}
      <div className="rounded-md  p-3 border border-border">
        <AudioFadeControls
          overlay={localOverlay}
          onOverlayChange={updateOverlay}
        />
      </div>

      {/* 变速和时长控制 */}
      <MediaControls
        overlay={localOverlay}
        onOverlayPropertyChange={updateOverlay}
        minSpeed={0.25}
        maxSpeed={4}
        speedStep={0.25}
        minDuration={0.1}
        maxDuration={60}
        durationStep={0.1}
      />
    </div>
  )
}
