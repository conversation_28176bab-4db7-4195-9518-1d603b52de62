import React from 'react'
import { StickerOverlay, VideoOverlay } from '@clipnest/remotion-shared/types'
import { FormSlider } from './form-components'

interface MediaPaddingControlsProps {
  localOverlay: VideoOverlay | StickerOverlay
  handleStyleChange: (
    updates: Partial<VideoOverlay | StickerOverlay>,
    commit?: boolean
  ) => void
}

export const MediaPaddingControls: React.FC<MediaPaddingControlsProps> = ({
  localOverlay,
  handleStyleChange,
}) => {
  const paddingValue = localOverlay?.styles?.padding || '0px'
  const paddingMatch = paddingValue.match(/^(\d+)px$/)
  const numericPadding = paddingMatch ? parseInt(paddingMatch[1], 10) : 0

  const paddingBackgroundColor
    = localOverlay?.styles?.paddingBackgroundColor || 'transparent'

  return (
    <div className="space-y-4">
      {/* Padding Control */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <label className="text-xs text-gray-600 dark:text-gray-400">
            边距
          </label>
          <span className="text-xs text-gray-600 dark:text-gray-400 min-w-[40px] text-right">
            {paddingValue}
          </span>
        </div>
        <FormSlider
          min={0}
          max={100}
          step={5}
          value={numericPadding}
          onChange={(e, commit) =>
            handleStyleChange({ styles: { padding: `${e}px` } }, commit)}
        />

      </div>

      {/* Padding Background Color */}
      <div >
        <label className="text-xs text-gray-600 dark:text-gray-400">
          背景颜色
        </label>
        <div className="flex items-center mt-2 gap-2">
          <input
            type="color"
            value={
              paddingBackgroundColor === 'transparent'
                ? '#ffffff'
                : paddingBackgroundColor
            }
            onChange={e =>
              handleStyleChange({ styles: { paddingBackgroundColor: e.target.value } }, true)}
            className="w-8 h-8 border border-gray-200 dark:border-gray-700 rounded-md p-0.5 cursor-pointer"
          />
          <input
            type="text"
            value={paddingBackgroundColor}
            onChange={e =>
              handleStyleChange({ styles: { paddingBackgroundColor: e.target.value } }, true)}
            placeholder="transparent"
            className="flex-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md text-xs p-2 hover:border-gray-300 dark:hover:border-gray-600 transition-colors text-gray-900 dark:text-gray-100"
          />
          {paddingBackgroundColor !== 'transparent' && (
            <button
              onClick={() =>
                handleStyleChange({ styles: { paddingBackgroundColor: 'transparent' } }, true)}
              className="text-xs text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200"
            >
              Clear
            </button>
          )}
        </div>
      </div>
    </div>
  )
}
