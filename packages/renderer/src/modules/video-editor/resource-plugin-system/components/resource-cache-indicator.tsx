import React from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types'
import { Loader2Icon, PlusIcon } from 'lucide-react'
import { cn } from '@/components/lib/utils'
import { queryClient } from '@/main'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { useResourceLoadingStore } from '@/modules/video-editor/hooks/resource/useResourceLoadingStore'
import { useResource } from '@/modules/video-editor/hooks/resource/useResource'

export interface ResourceCacheIndicatorProps {
  /**
   * 资源类型
   */
  resourceType: ResourceType
  /**
   * 资源URL
   */
  resourceUrl: string

  /**
   * 是否正在加载
   */
  isLoading?: boolean
  /**
   * 图标大小
   */
  size?: number
  /**
   * 自定义类名
   */
  className?: string

  onAddTimeline?: () => void
  version?: string

  isCached?: boolean

  isChecking?: boolean

}

export function ResourceCacheIndicator({
  resourceType,
  resourceUrl,
  size = 16,
  className,
  version,
  onAddTimeline,
  isCached = false,
  isChecking,
}: ResourceCacheIndicatorProps) {
  const { downloadResourceToCache } = useResource()
  const { startResourceLoading, endResourceLoading, _generateKey } = useResourceLoadingStore()

  const loadingStates = useResourceLoadingStore(state => state.loadingStates)

  const isLoading = loadingStates.get(_generateKey(resourceUrl, resourceType))?.isLoading || false

  const handleDownload = async (e: React.MouseEvent) => {
    e.stopPropagation()
    try {
      startResourceLoading(resourceUrl, resourceType)
      await downloadResourceToCache({
        url: resourceUrl,
        resourceType,
        version: version,
        customExt: resourceType === ResourceType.MUSIC ? 'mp3' : undefined
      })
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.RESOURCE_CACHE_STATUS, resourceType, resourceUrl] })
      onAddTimeline?.()
      endResourceLoading(resourceUrl, resourceType)
    } catch (error) {
      console.error('下载资源失败:', error)
      endResourceLoading(resourceUrl, resourceType)
    }
  }

  return (

    <div
      className={cn(
        'absolute right-0 bottom-0 transition-opacity duration-200',
        isLoading ? 'opacity-100' : 'opacity-0 group-hover:opacity-100',
        'flex items-center justify-center bg-gray-900/80 rounded p-1 cursor-pointer text-gray-500 hover:bg-gray-900/50 transition-all',
        className
      )}
    >
      {
        (isLoading || isChecking) ? (
          <Loader2Icon
            className="animate-spin text-blue-500 cursor-pointer"
            style={{ width: size, height: size }}
          />
        ) :
          <button
            onClick={handleDownload}
          >
            <PlusIcon
              className={
                cn(isCached ? 'hover:text-green-400 text-green-500' : 'text-gray-400 hover:text-blue-500', ' cursor-pointer')
              }
              style={{ width: size, height: size }}
            />
          </button>
      }
    </div>

  )
}
