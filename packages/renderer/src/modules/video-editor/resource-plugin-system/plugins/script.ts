import React from 'react'
import { Camera } from 'lucide-react'
import { OverlayType } from '@clipnest/remotion-shared/types'
import { registerMaterialPlugin } from '../registry'
import { ResourceType } from '@app/shared/types/resource-cache.types'

const Panel = React.lazy(() =>
  import('@/modules/video-editor/resource-plugin-system/plugin-panels/script.panel')
)

export default registerMaterialPlugin({
  id: ResourceType.SCRIPT,
  title: '脚本',
  icon: Camera,
  component: Panel,
  overlayType: OverlayType.SOUND,
  order: 0,
})
