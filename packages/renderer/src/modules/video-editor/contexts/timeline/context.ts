import React, { createContext, useContext } from 'react'

import { TimelineZoomHook } from './useTimelineZoom'
import { TimelineClipboard } from './useTimelineClipboard'
import { TimelineSnappingHook } from './useTimelineSnapping'
import { TimelineTrackDnDHook } from './useTimelineTrackDnD'
import { TimelineOverlayDnDHook } from './useTimelineOverlayDnD'
import { TimelineTracksLayout } from './useTimelineTracksLayout'
import { TimelineOverlayActivation } from './useTimelineOverlayActivation'

/**
 * Context interface for managing timeline state and interactions.
 * @interface TimelineContextType
 */
interface TimelineContextType extends
  TimelineTrackDnDHook,
  TimelineOverlayDnDHook,
  TimelineZoomHook,
  TimelineOverlayActivation,
  Omit<TimelineSnappingHook, 'snappedLandingPoint'>
{
  layout: TimelineTracksLayout
  clipboard: TimelineClipboard

  isContextMenuOpen: boolean,
  setIsContextMenuOpen(v: boolean): void

  /** Reference to the timeline grid DOM element */
  timelineGridRef: React.RefObject<HTMLDivElement | null>
}

/**
 * Context for sharing timeline state and functionality across components.
 */
export const TimelineContext = createContext<TimelineContextType>({} as any)

export const useTimeline = () => {
  const context = useContext(TimelineContext)
  if (!context) {
    throw new Error('useTimeline must be used within a TimelineProvider')
  }
  return context
}
