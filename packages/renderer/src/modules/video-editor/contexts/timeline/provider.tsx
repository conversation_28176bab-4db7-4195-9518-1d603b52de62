import React, { useRef, useState } from 'react'
import { useTimelineClipboard } from '@/modules/video-editor/contexts/timeline/useTimelineClipboard'
import { useTimelineZoom } from '@/modules/video-editor/contexts/timeline/useTimelineZoom'
import { useTimelineTrackDnD } from '@/modules/video-editor/contexts/timeline/useTimelineTrackDnD'
import { useTimelineOverlayActivation } from '@/modules/video-editor/contexts/timeline/useTimelineOverlayActivation'
import { useTimelineTracksLayout } from '@/modules/video-editor/contexts/timeline/useTimelineTracksLayout'
import { useTimelineOverlayDnD } from '@/modules/video-editor/contexts/timeline/useTimelineOverlayDnD'
import { useTimelineSnapping } from '@/modules/video-editor/contexts/timeline/useTimelineSnapping'
import { SNAPPING_CONFIG } from '@/modules/video-editor/constants'
import { TimelineContext } from '@/modules/video-editor/contexts'

/**
 * Provider component that manages timeline state and makes it available to child components.
 * Combines functionality from multiple hooks to handle timeline interactions.
 *
 * @component
 */
export const TimelineProvider: React.FC<{ children: React.ReactNode }> = ({
  children
}) => {
  // State for context menu visibility
  const [isContextMenuOpen, setIsContextMenuOpen] = useState(false)

  const timelineGridRef = useRef<HTMLDivElement>(null)

  const clipboard = useTimelineClipboard()
  const zoom = useTimelineZoom(timelineGridRef)
  const trackDragAndDrop = useTimelineTrackDnD()
  const overlaySelection = useTimelineOverlayActivation()

  const layout = useTimelineTracksLayout()

  const { landingPoint, ...timelineState } = useTimelineOverlayDnD(
    timelineGridRef,
    zoom.zoomScale,
  )

  const { snappedLandingPoint, alignmentLines } = useTimelineSnapping({
    landingPoint,
    isDragging: timelineState.isDragging,
    draggingOverlay: timelineState.draggingOverlay,
    dragInfo: timelineState.dragInfo,
    snapThreshold: SNAPPING_CONFIG.thresholdFrames,
  })

  return (
    <TimelineContext.Provider
      value={{
        ...trackDragAndDrop,
        ...zoom,
        ...timelineState,
        ...overlaySelection,
        clipboard,
        layout,

        isContextMenuOpen,
        setIsContextMenuOpen,

        alignmentLines,
        landingPoint: snappedLandingPoint,
        timelineGridRef,
      }}
    >
      {children}
    </TimelineContext.Provider>
  )
}
