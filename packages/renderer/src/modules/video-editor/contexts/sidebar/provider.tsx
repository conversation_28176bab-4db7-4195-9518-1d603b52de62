import React, { useState } from 'react'
import { useSidebar as useUISidebar } from '@/components/ui/sidebar'
import { ResourceType } from '@app/shared/types/resource-cache.types'
import { SidebarContext } from './context'

// Provider 组件，包装需要访问 sidebar 状态的应用部分
export const SidebarProvider: React.FC<React.PropsWithChildren> = ({
  children,
}) => {
  const [activePanel, setActivePanel] = useState<ResourceType>(null as any)
  const { setOpen } = useUISidebar()

  const value = {
    activePanel,
    setActivePanel,
    setIsOpen: setOpen,
  }

  return (
    <SidebarContext.Provider value={value}>{children}</SidebarContext.Provider>
  )
}
