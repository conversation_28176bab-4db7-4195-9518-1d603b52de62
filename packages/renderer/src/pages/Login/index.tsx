import React, { useEffect } from 'react'
import { Outlet, useNavigate } from 'react-router'
import './login.css'
import { Button } from '@/components/ui/button'
import { TeamManager, TokenManager } from '@/libs/storage'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTrigger } from '@/components/ui/dialog'
import { genForm } from '@/libs/tools/form'
import { z } from 'zod'
import { useQueryClient } from '@tanstack/react-query'

// function useTemporaryRem(baseWidth: number, baseFontSize: number = 16) {
//   useEffect(() => {
//     const root = document.documentElement
//     const originalFontSize = root.style.fontSize
//
//     const setFontSize = () => {
//       const size = (window.innerWidth / baseWidth) * baseFontSize
//       root.style.fontSize = `${size}px`
//     }
//
//     setFontSize()
//     window.addEventListener('resize', setFontSize)
//
//     return () => {
//       window.removeEventListener('resize', setFontSize)
//       root.style.fontSize = originalFontSize // ✅ 恢复初始值
//     }
//   }, [])
// }

const Form = genForm(
  z.object({
    accessToken: z.string().default('test1'),
    refreshToken: z.string().default('test1'),
    expiresTime: z
      .string()
      .default('1e20')
      .transform(val => {
        const num = Number(val)
        return isNaN(num) ? 1e20 : num
      }),
    openid: z.string().default(''),
    userId: z
      .string()
      .default('1')
      .transform(val => {
        const num = Number(val)
        return isNaN(num) ? 1 : num
      }),
    tenantId: z
      .string()
      .default('1')
      .transform(val => {
        const num = Number(val)
        return isNaN(num) ? 1 : num
      }),
  }),
  {
    fields: {
      accessToken: { label: 'Access Token', element: 'input', props: { placeholder: 'test1' } },
      refreshToken: { label: 'Refresh Token', element: 'input', props: { placeholder: 'test1' } },
      expiresTime: { label: 'Expires Time', element: 'input', props: { placeholder: '1e20' } },
      openid: { label: 'OpenID', element: 'input', props: { placeholder: '' } },
      userId: { label: 'User ID', element: 'input', props: { placeholder: '1' } },
      tenantId: { label: 'Tenant ID', element: 'input', props: { placeholder: '1' } },
    },
  },
)

export default function Login() {
  const queryClient = useQueryClient()
  const navigate = useNavigate()

  useEffect(() => {
    const isDark = document.documentElement.classList.contains('dark')
    if (isDark) return
    document.documentElement.classList.add('dark')
    return () => {
      document.documentElement.classList.remove('dark')
    }
  })

  useEffect(() => {
    const root = document.documentElement
    const originalFontSize = root.style.fontSize

    const setFontSize = () => {
      let size = (window.innerWidth / 1920) * 16
      console.log(window.innerWidth / size)
      if (window.innerWidth < 640) size *= 2
      root.style.fontSize = `${size}px`
    }

    setFontSize()
    window.addEventListener('resize', setFontSize)

    return () => {
      window.removeEventListener('resize', setFontSize)
      root.style.fontSize = originalFontSize // ✅ 恢复初始值
    }
  }, [])

  return (
    <div
      className="h-full bg-[url(/images/bg-login.jpg)]
        bg-cover bg-center bg-no-repeat relative
        before:content-[''] before:h-full before:inset-0 before:absolute before:bg-black/50
        *:relative *:z-10 flex items-center"
    >
      <Dialog>
        <DialogTrigger asChild>
          <Button size="lg" className="absolute! right-4 top-4">
            开发者登录
          </Button>
        </DialogTrigger>
        <DialogContent className="w-[400px]">
          <DialogHeader>
            <h2 className="text-2xl font-bold">开发者登录</h2>
          </DialogHeader>
          <DialogDescription>此登录方式仅用于开发和测试</DialogDescription>
          <Form
            onSubmit={async data => {
              TokenManager.saveLoginData(data)
              TeamManager.switch(data.tenantId)
              await queryClient.resetQueries()
              navigate('/', { replace: true })
            }}
          >
            <Button type="submit" className="w-full">
              登录
            </Button>
          </Form>
        </DialogContent>
      </Dialog>
      <div className="ml-40 text-7xl leading-25 select-none font-[ShuHeiti] font-semibold whitespace-nowrap hidden sm:block">
        <span className="text-gradient-brand">AI视频</span>智造新次元
        <br />从<span className="text-gradient-brand">创作到分发</span>，<br />
        十倍效能跃迁
      </div>
      <div className="absolute! -translate-x-1/2 sm:translate-x-0 left-1/2 login-box w-170 h-180 rounded-[20px] border-2 *:blur-sm *:last:blur-none">
        <div className="size-5/6 absolute -top-[3px] -left-[3px] middle-1" />
        <div className="size-5/6 absolute -top-[4px] -left-[4px] outer-1" />
        <div className="size-5/6 absolute top-0 left-0 inner-1-1" />
        <div className="size-5/6 absolute top-0 left-0 inner-1-2" />
        <div className="size-5/6 rotate-180 absolute -bottom-[3px] -right-[3px] middle-2" />
        <div className="size-5/6 rotate-180 absolute -bottom-[4px] -right-[4px] outer-2" />
        <div className="size-5/6 rotate-180 absolute bottom-0 right-0 inner-2-1" />
        <div className="size-5/6 rotate-180 absolute bottom-0 right-0 inner-2-2" />
        <div className="login-content size-full relative rounded-[20px]">
          <Outlet />
        </div>
      </div>
    </div>
  )
}
