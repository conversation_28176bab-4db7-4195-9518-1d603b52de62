import React from 'react'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { UploadIcon, FolderUpIcon, ChevronDown } from 'lucide-react'
import { FileUploader, FileUploaderRenderProps } from '@/components/ui/file-uploader'
import { FolderUploader } from '@/components/ui/folder-uploader'

interface UploadMaterialProps {
  folderUuid: string
  resourceType?: 'material' | 'paster' | 'music' | 'voice'
  onUpload?: () => Promise<void> | void
  buttonLabel?: string
  customFileUploaderRender?: (props: FileUploaderRenderProps) => React.ReactNode
}

const UploadMaterial: React.FC<UploadMaterialProps> = ({
  folderUuid,
  resourceType = 'material',
  onUpload,
  buttonLabel = '上传',
  customFileUploaderRender,
}) => {
  const DefaultFileUploader = ({ getRootProps, getInputProps, isLoading }: FileUploaderRenderProps) => (
    <div className="flex items-center justify-center gap-2">
      <UploadIcon className="w-3.5 h-3.5" />
      <div {...getRootProps()}>
        <input {...getInputProps()} />
        {isLoading ? '上传中...' : '上传文件'}
      </div>
    </div>
  )

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="w-36 border-0 bg-primary/10 flex justify-between gap-1">
          <span>{buttonLabel}</span>
          <ChevronDown className="w-3.5 h-3.5" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-36 p-0">
        <div className="py-1">
          {/* 上传文件 */}
          <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center gap-2">
            <FileUploader
              folderUuid={folderUuid}
              renderCustomComponent={customFileUploaderRender || DefaultFileUploader}
            />
          </button>

          {/* 上传文件夹 */}
          <button className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center gap-2">
            <FolderUploader
              resourceType={resourceType}
              folderUuid={folderUuid}
              children={
                <div className="flex items-center justify-center">
                  <FolderUpIcon className="w-3.5 h-3.5 mr-2" />
                  上传文件夹
                </div>
              }
              isShowUploadedFiles={false}
              showFileList={false}
              onUpload={onUpload}
            />
          </button>
        </div>
      </PopoverContent>
    </Popover>
  )
}
export default UploadMaterial
