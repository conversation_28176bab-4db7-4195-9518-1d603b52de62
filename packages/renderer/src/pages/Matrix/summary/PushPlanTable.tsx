import React from 'react'
import { ColumnDef } from '@tanstack/react-table'
import { DataTable } from '@/components/ui/data-table'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Eye } from 'lucide-react'

import { usePaginationDyPushPlanList } from '@/hooks/queries/useQueryMatrix'
import {  useNavigate } from 'react-router'
import { DyPushPlan } from '@/types/matrix/douyin'

export const PushPlanTable = () => {
  const {
    data,
    pagination: paginationInfo,
    setPagination,
    isLoading,
  } = usePaginationDyPushPlanList({}, 10)

  const navigate = useNavigate()
  const getStatusBadge = (status?: number) => {
    const statusMap = {
      0: { label: '待发布', variant: 'secondary' as const },
      1: { label: '发布中', variant: 'default' as const },
      2: { label: '失败', variant: 'destructive' as const },
      3: { label: '成功', variant: 'success' as const },
      4: { label: '部分成功', variant: 'warning' as const },
    }

    const statusInfo = statusMap[status as keyof typeof statusMap] || statusMap[0]
    return (
      <Badge variant={statusInfo.variant}>
        {statusInfo.label}
      </Badge>
    )
  }

  const formatDate = (timestamp?: number) => {
    if (!timestamp) return '-'
    return new Date(timestamp).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const columns: ColumnDef<DyPushPlan>[] = [
    {
      accessorKey: 'id',
      header: 'ID',
      cell: ({ row }) => (
        <div className="">
          {row.getValue('id') || '-'}
        </div>
      ),
    },
    {
      accessorKey: 'name',
      header: '计划名称',
      cell: ({ row }) => (
        <div className="font-medium max-w-[200px] truncate">
          {row.getValue('name') || '-'}
        </div>
      ),
    },
    {
      accessorKey: 'totalAccount',
      header: '账号数',
      cell: ({ row }) => (
        <div className="">
          {row.getValue('totalAccount') || 0}
        </div>
      ),
    },
    {
      accessorKey: 'totalVideo',
      header: '视频数',
      cell: ({ row }) => (
        <div className="">
          {row.getValue('totalVideo') || 0}
        </div>
      ),
    },
    {
      accessorKey: 'wait',
      header: '待发布',
      cell: ({ row }) => (
        <div className=" ">
          {row.getValue('wait') || 0}
        </div>
      ),
    },
    {
      accessorKey: 'doing',
      header: '发布中',
      cell: ({ row }) => (
        <div className=" text-blue-600">
          {row.getValue('doing') || 0}
        </div>
      ),
    },
    {
      accessorKey: 'success',
      header: '成功',
      cell: ({ row }) => (
        <div className=" text-green-600">
          {row.getValue('success') || 0}
        </div>
      ),
    },
    {
      accessorKey: 'fail',
      header: '失败',
      cell: ({ row }) => (
        <div className=" text-red-600">
          {row.getValue('fail') || 0}
        </div>
      ),
    },
    {
      accessorKey: 'status',
      header: '状态',
      cell: ({ row }) => getStatusBadge(row.getValue('status')),
    },
    {
      accessorKey: 'publishTime',
      header: '发布时间',
      cell: ({ row }) => (
        <div className="text-sm  min-w-[120px]">
          {formatDate(row.getValue('publishTime'))}
        </div>
      ),
    },
    {
      accessorKey: 'createTime',
      header: '创建时间',
      cell: ({ row }) => (
        <div className="text-sm  min-w-[120px]">
          {formatDate(row.getValue('createTime'))}
        </div>
      ),
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const planId = row.getValue('id')

        return (
          <Button variant="link"
            size="sm"
            onClick={() => {
              navigate(`/home/<USER>/planDetail/${planId}`)
            }}
          >
            <Eye className="mr-2 h-4 w-4" />
            查看详情
          </Button>

        )
      }
    },
  ]

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gradient-brand">视频发布计划</h2>

        <div className="text-sm text-gray-500">
          共 {paginationInfo.total} 条记录
        </div>
      </div>

      <DataTable
        columns={columns}
        data={data}
        loading={isLoading}
        pagination={paginationInfo}
        onPaginationChange={setPagination}
        emptyMessage="暂无发布计划"
      />
    </div>
  )
}
