import React, { useState, useEffect, useRef } from 'react'
import { Ellipsis, ChevronDown, ChevronRight } from 'lucide-react'
import { MaterialResource } from '@/types/resources'
import { cn } from '@/components/lib/utils'
import { createPortal } from 'react-dom'

export type TreeNode = {
  id: string
  label: string
  children?: TreeNode[]
  raw?: MaterialResource.Directory | MaterialResource.LocalDirectory
}

export type TreeAction = {
  icon: React.ReactNode
  label: string
  onClick?: (nodeId: string, parentId?: string, label?: string) => void
}

interface TreeListProps {
  /**
   * 树形结构的数据
   */
  data: TreeNode[]
  /**
   * 是否全部展开，默认不展开
   */
  defaultExpandAll?: boolean
  /**
   * 外层样式
   */
  className?: string
  /**
   * 节点的可用操作
   */
  actions?: TreeAction[]
  /**
   * 节点右侧的图标，鼠标悬浮时弹出操作菜单，在actions有值时才显示
   * false(默认)：直接显示第一个 action 的按钮
   * true：是显示图标 (⋯)，鼠标悬浮时弹出操作菜单
   */
  showEllipsis?: boolean
  /**
   * 当前选中的节点 ID
   */
  selectedId?: string
  keyword?: string
  selectStyle?: string
  /**
   * 点击某个节点时触发，返回被选中的 TreeNode 对象
   */
  onSelect?: (node: TreeNode) => void
}

/**
 * 转为树结构
 */
export function buildTreeFromFlatList(list: MaterialResource.Directory[]): TreeNode[] {
  const map = new Map<string, TreeNode>()
  const tree: TreeNode[] = []

  // 初始化 TreeNode 节点
  list.forEach(item => {
    map.set(item.folderId, {
      id: item.folderId,
      label: item.folderName,
      children: [],
      raw: item,
    })
  })

  list.forEach(item => {
    const node = map.get(item.folderId)!
    if (!item.parentId || !map.has(item.parentId)) {
      // 顶级节点
      tree.push(node)
    } else {
      const parent = map.get(item.parentId)
      parent?.children?.push(node)
    }
  })

  return tree
}

// 根据树结构找到某个节点的完整路径链
export const getPathChain = (tree: any[], targetId: string, path: any[] = []): any[] | null => {
  for (const node of tree) {
    const newPath = [...path, node]
    if (node.id === targetId) return newPath
    if (node.children) {
      const result = getPathChain(node.children, targetId, newPath)
      if (result) return result
    }
  }
  return null
}

//当前节点是否有效
export const isValidFolderId = (tree: any[], id: string): boolean => {
  for (const node of tree) {
    if (node.id === id) return true
    if (node.children?.length && isValidFolderId(node.children, id)) return true
  }
  return false
}

const TreeItem: React.FC<{
  node: TreeNode
  parentId?: string
  actions?: TreeAction[]
  showEllipsis?: boolean
  selectedId?: string
  defaultExpandAll?: boolean
  keyword?: string
  selectStyle?: string
  onSelect?: (node: TreeNode) => void
}> = ({ node, parentId, actions, showEllipsis, selectedId, defaultExpandAll, keyword, selectStyle, onSelect }) => {
  const [open, setOpen] = useState(defaultExpandAll ?? false)
  const [popup, setPopup] = useState(false)
  const hasChildren = node.children && node.children.length > 0
  const baseClass = showEllipsis ? '' : 'min-w-[120px]'
  const hasActions = actions && actions.length > 0
  const ellipsisRef = useRef<HTMLSpanElement>(null)

  useEffect(() => {
    if (keyword && node.children?.length) {
      const hasMatch = node.children.some(child => child.label.includes(keyword))
      if (hasMatch) setOpen(true)
    }
  }, [keyword, node.children])

  return (
    <div className="ml-4 relative">
      <div
        className={cn(
          'inline-flex items-center cursor-pointer select-none rounded px-2 py-1 mb-1 relative',
          selectedId === node.id ? selectStyle || 'bg-accent' : 'hover:bg-primary/20',
        )}
      >
        {hasChildren && (
          <span className="mr-1" onClick={() => hasChildren && setOpen(!open)}>
            {open ? <ChevronDown className="w-4 h-4" /> : <ChevronRight className="w-4 h-4" />}
          </span>
        )}
        <div
          className="flex items-center"
          onClick={() => {
            onSelect?.(node)
          }}
        >
          <span className={cn('whitespace-nowrap', baseClass)}>{node.label}</span>

          {hasActions && (
            <>
              {!showEllipsis && actions && (
                <span
                  className="ml-2 cursor-pointer"
                  onClick={e => {
                    e.stopPropagation()
                    actions[0].onClick?.(node.id, parentId, node.label)
                  }}
                >
                  {actions[0].icon}
                </span>
              )}
              {showEllipsis && actions && (
                <span
                  ref={ellipsisRef}
                  className="ml-2 cursor-pointer pr-2"
                  onMouseEnter={() => setPopup(true)}
                  onMouseLeave={() => setPopup(false)}
                  onClick={e => e.stopPropagation()}
                >
                  <Ellipsis className="w-4 h-4" />
                  {popup &&
                    createPortal(
                      (() => {
                        const rect = ellipsisRef.current?.getBoundingClientRect()
                        if (!rect) return null
                        return (
                          <div
                            style={{
                              position: 'absolute',
                              top: rect.bottom + window.scrollY,
                              left: rect.left + window.scrollX,
                              zIndex: 9999,
                            }}
                            className="bg-white dark:bg-neutral-800 border rounded shadow-lg min-w-[120px] py-2 px-4"
                          >
                            {actions
                              .filter(
                                action =>
                                  !(node.raw?.parentId === null || node.raw?.parentId === undefined) ||
                                  action.label === '新建文件夹',
                              )
                              .map((action, idx) => (
                                <div
                                  key={idx}
                                  className="flex items-center px-3 py-1 cursor-pointer hover:bg-neutral-100 dark:hover:bg-neutral-700"
                                  onClick={e => {
                                    e.stopPropagation()
                                    action.onClick?.(node.id, parentId, node.label)
                                  }}
                                >
                                  {action.icon}
                                  <span className="ml-2">{action.label}</span>
                                </div>
                              ))}
                          </div>
                        )
                      })(),
                      document.body,
                    )}
                </span>
              )}
            </>
          )}
        </div>
      </div>
      {hasChildren && open && (
        <div>
          {node.children!.map(child => (
            <TreeItem
              key={child.id}
              node={child}
              parentId={node.id}
              actions={actions}
              showEllipsis={showEllipsis}
              selectedId={selectedId}
              selectStyle={selectStyle}
              onSelect={onSelect}
            />
          ))}
        </div>
      )}
    </div>
  )
}

const TreeList: React.FC<TreeListProps> = ({
  data,
  defaultExpandAll = false,
  className,
  actions = [],
  showEllipsis = false,
  selectedId: externalSelectedId,
  keyword = '',
  selectStyle = '',
  onSelect,
}) => {
  const [internalSelectedId, setInternalSelectedId] = useState<string | undefined>(undefined)
  const handleSelect = (node: TreeNode) => {
    // 如果是非受控组件，就自己管理状态
    if (externalSelectedId === undefined) {
      setInternalSelectedId(node.id)
    }
    onSelect?.(node)
  }
  const effectiveSelectedId = externalSelectedId !== undefined ? externalSelectedId : internalSelectedId
  if (!data || data.length === 0) {
    return <div className={className}>暂无数据</div>
  }

  return (
    <div className={className}>
      {data.map(node => (
        <TreeItem
          key={node.id}
          node={node}
          parentId={undefined} // 顶层节点没有父 ID
          actions={actions}
          showEllipsis={showEllipsis}
          selectedId={effectiveSelectedId}
          defaultExpandAll={defaultExpandAll}
          keyword={keyword}
          selectStyle={selectStyle}
          onSelect={handleSelect}
        />
      ))}
    </div>
  )
}

export default TreeList
