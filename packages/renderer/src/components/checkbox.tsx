'use client'

import React, { create<PERSON>ontext, ReactNode, use<PERSON><PERSON>back, useContext, useEffect, useMemo, useState } from 'react'
import * as CheckboxPrimitive from '@radix-ui/react-checkbox'
import { Check, Minus } from 'lucide-react'
import { cn } from '@/components/lib/utils'

interface TableCheckboxInternalContextProps<T = any> {
  selected: Set<string>
  mounted: Record<string, T>
  check: (...keys: string[]) => void
  uncheck: (...keys: string[]) => void
  register: (key: string, value: T) => void
  unregister: (key: string) => void
}

interface TableCheckboxContextProps<T = any> {
  selected: Record<string, T>
  all: Record<string, T>
  setSelected: (id: string, status: boolean) => void
}

const TableCheckboxInternalContext = createContext<TableCheckboxInternalContextProps | null>(null)
const TableCheckboxContext = createContext<TableCheckboxContextProps | null>(null)

function useInternalContext<T>() {
  const context = useContext(TableCheckboxInternalContext)
  if (!context) {
    throw new Error('CheckboxProvider is missing')
  }
  return context as TableCheckboxInternalContextProps<T>
}

export function useCheckboxContext<T>() {
  const context = useContext(TableCheckboxContext)
  if (!context) {
    throw new Error('CheckboxProvider is missing')
  }
  return context as TableCheckboxContextProps<T>
}

export function Provider<T>({
  children,
  initialValue,
  mode = 'remove',
}: {
  children: ReactNode
  initialValue?: Record<string, T>
  mode?: 'remove' | 'keep'
}) {
  const [selected, setSelected] = useState(new Set<string>())
  const [mounted, setMounted] = useState(initialValue ?? {})

  const check = useCallback((...keys: string[]) => {
    setSelected(prev => new Set([...prev, ...keys]))
  }, [])

  const uncheck = useCallback((...keys: string[]) => {
    const set = new Set(keys)
    setSelected(prev => new Set([...prev].filter(key => !set.has(key))))
  }, [])

  const register = useCallback((id: string, value: T) => {
    setMounted(prev => ({ ...prev, [id]: value }))
  }, [])

  const unregister = useCallback(
    (id: string) => {
      setMounted(({ [id]: _, ...rest }) => rest)
      if (mode === 'keep') return
      setSelected(prev => (prev.delete(id), new Set([...prev])))
    },
    [mode],
  )

  const internal: TableCheckboxInternalContextProps<T> = {
    selected,
    mounted,
    check,
    uncheck,
    register,
    unregister,
  }

  const setSelectedCallback = useCallback(
    (id: string, status: boolean) => {
      if (status) check(id)
      else uncheck(id)
    },
    [selected, check, uncheck],
  )

  const context: TableCheckboxContextProps<T> = useMemo(
    () => ({
      selected: Object.fromEntries(Array.from(selected.values()).map(key => [key, mounted[key]])),
      all: mounted,
      setSelected: setSelectedCallback,
    }),
    [selected, mounted],
  )

  return (
    <TableCheckboxInternalContext.Provider value={internal}>
      <TableCheckboxContext.Provider value={context}>{children}</TableCheckboxContext.Provider>
    </TableCheckboxInternalContext.Provider>
  )
}

export function Header({
  className,
  onClick,
  ...props
}: Omit<React.ComponentProps<typeof CheckboxPrimitive.Root>, 'checked'>) {
  const { selected, mounted, check, uncheck } = useInternalContext()
  const [checked, setChecked] = useState<boolean | 'indeterminate'>(false)

  useEffect(() => {
    if (!Object.keys(mounted).length) return
    const a = Object.keys(mounted).some(key => selected.has(key))
    const b = Object.keys(mounted).some(key => !selected.has(key))
    setChecked(a && b ? 'indeterminate' : a)
  }, [mounted, selected])

  const handleSelectAll: React.MouseEventHandler<HTMLButtonElement> = e => {
    const action = checked ? uncheck : check
    action(...Object.keys(mounted))
    onClick?.(e)
  }

  return (
    <CheckboxPrimitive.Root
      {...props}
      checked={checked}
      onClick={handleSelectAll}
      className={cn(
        'flex items-center justify-center',
        'size-[14px] rounded-[2px]',
        'bg-primary aria-[checked=false]:bg-transparent',
        'border aria-checked:border-primary aria-[checked=mixed]:border-primary',
        className,
      )}
    >
      {checked === true && (
        <CheckboxPrimitive.Indicator className="flex items-center justify-center">
          <Check className="size-4 text-primary-foreground" />
        </CheckboxPrimitive.Indicator>
      )}
      {checked === 'indeterminate' && (
        <CheckboxPrimitive.Indicator className="flex items-center justify-center">
          <Minus className="size-4 text-primary-foreground" />
        </CheckboxPrimitive.Indicator>
      )}
    </CheckboxPrimitive.Root>
  )
}

export function Item<T>({
  id,
  value,
  disabled,
  className,
  ...props
}: {
  id?: string
  value: T
} & Omit<React.ComponentProps<typeof CheckboxPrimitive.Root>, 'checked'>) {
  const { selected, register, unregister, check, uncheck } = useInternalContext<T | undefined>()

  const key = useMemo(() => id ?? String(value), [id, value])

  useEffect(() => {
    register(key, value)
    return () => {
      unregister(key)
    }
  }, [id, value, register, unregister])

  const checked = selected.has(key)

  const handleSelect = () => {
    if (checked) uncheck(key)
    else check(key)
  }

  return (
    <CheckboxPrimitive.Root
      {...props}
      disabled={disabled}
      checked={checked}
      onClick={handleSelect}
      className={cn(
        'flex items-center justify-center',
        'size-[14px] rounded-[2px]',
        'bg-primary aria-[checked=false]:bg-transparent',
        'border aria-checked:border-primary aria-[checked=mixed]:border-primary',
        className,
      )}
    >
      {checked === true && (
        <CheckboxPrimitive.Indicator className="flex items-center justify-center">
          <Check className="size-4 text-primary-foreground" />
        </CheckboxPrimitive.Indicator>
      )}
    </CheckboxPrimitive.Root>
  )
}

export { Provider as CheckboxProvider, Header as CheckboxHeader, Item as CheckboxItem }
