import { Loader2 } from 'lucide-react'
import { But<PERSON> } from './ui/button'
import React, { ReactNode, useEffect } from 'react'
import { ModalContext, useModalContext } from '@/libs/tools/modal'
import * as DIALOG from '@/components/ui/dialog'
import * as SHEET from '@/components/ui/sheet'

interface ModalLib {
  Title: typeof DIALOG.DialogTitle
  Header: typeof DIALOG.DialogHeader
  Description: typeof DIALOG.DialogDescription
  Close: typeof DIALOG.DialogClose
  Footer: typeof DIALOG.DialogFooter
}

const modals: Record<string, ModalLib> = {
  dialog: {
    Title: DIALOG.DialogTitle,
    Header: DIALOG.DialogHeader,
    Description: DIALOG.DialogDescription,
    Close: DIALOG.DialogClose,
    Footer: DIALOG.DialogFooter,
  },
  sheet: {
    Title: SHEET.SheetTitle as any,
    Header: SHEET.SheetHeader,
    Description: SHEET.SheetDescription as any,
    Close: SHEET.SheetClose as any,
    Footer: SHEET.SheetFooter,
  },
}

export interface ModalHeaderProps {
  title: ReactNode
  description?: ReactNode
}

export function ModalHeader({ title, description }: ModalHeaderProps) {
  const { variant } = useModalContext()
  const { Header, Title, Description } = modals[variant]

  return (
    <Header>
      <Title>{title}</Title>
      {description && <Description>{description}</Description>}
    </Header>
  )
}

export interface CustomButton {
  label: string
  onClick: () => void
}

export type ModalFooterButtonsProps = Pick<ModalContext, 'close'> &
  Pick<ModalFooterProps, 'type' | 'onCancel' | 'onConfirm'>

export interface ModalFooterProps {
  pending?: boolean
  type?: React.ComponentProps<typeof Button>['type']
  onCancel?: () => unknown
  onConfirm?: () => unknown
  children?: ReactNode
  buttons?: ReactNode | React.ComponentType<ModalFooterButtonsProps>
}

export function ModalFooter(props: ModalFooterProps) {
  const { pending, type = 'submit', onCancel, onConfirm, children, buttons: CustomButtons } = props
  const ctx = useModalContext()
  const { closed, variant } = ctx
  const { Footer, Close } = modals[variant]

  useEffect(() => {
    if (!closed) return
    onCancel?.()
  }, [closed, onCancel])

  const getButtons = () => {
    if (typeof CustomButtons !== 'function') return CustomButtons
    return <CustomButtons {...{ close: ctx.close, type, onCancel, onConfirm }} />
  }

  return (
    <Footer className="items-center mt-auto">
      <div className="mr-auto flex items-center">{children}</div>
      {getButtons() ?? (
        <>
          <Close asChild>
            <Button variant="outline" type="button" className="w-[60px] h-8 bg-white/5">
              取消
            </Button>
          </Close>
          <Button
            variant="default"
            className="w-[60px] h-8 bg-primary-highlight1 hover:bg-primary-highlight1/80 text-white"
            type={type}
            onClick={onConfirm}
          >
            {pending ? <Loader2 className="animate-spin size-4" /> : '确定'}
          </Button>
        </>
      )}
    </Footer>
  )
}

export interface ModalContentProps extends ModalHeaderProps, ModalFooterProps {
  children?: ReactNode
}

export function ModalContent({ children, ...props }: ModalContentProps) {
  return (
    <>
      <ModalHeader {...props} />
      {children}
      <ModalFooter {...props} />
    </>
  )
}
