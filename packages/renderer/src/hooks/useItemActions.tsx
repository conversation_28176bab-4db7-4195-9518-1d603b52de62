import React from 'react'
import { useQueryClient } from '@tanstack/react-query'
import { ResourceModule } from '@/libs/request/api/resource'
import { useDeleteModal } from '@/components/modal/delete'
import { useRenameMaterial, useCreateMaterial } from '@/pages/Projects/material/components/model'
import { ResourceSource } from '@/types/resources'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { Button } from '@/components/ui/button'
import { Loader2 } from 'lucide-react'
import { usePending } from './usePending'

const queryKeyMap: Record<ResourceSource, string> = {
  [ResourceSource.MEDIA]: QUERY_KEYS.MATERIAL_MEDIA_LIST,
  [ResourceSource.FOLDER]: QUERY_KEYS.MATERIAL_DIRECTORY_LIST,
  [ResourceSource.MULTI_SELECT]: QUERY_KEYS.MATERIAL_DIRECTORY_LIST,
  [ResourceSource.LOCAL_STICK_MULTI_SELECT]: QUERY_KEYS.LOCAL_PASTER_FOLDER_LIST,
  [ResourceSource.LOCAL_MUSIC_MULTI_SELECT]: QUERY_KEYS.LOCAL_MUSIC_FOLDER_LIST,
  [ResourceSource.LOCAL_SOUND_MULTI_SELECT]: QUERY_KEYS.LOCAL_SOUND_FOLDER_LIST,
  [ResourceSource.LOCAL_STICK]: QUERY_KEYS.LOCAL_PASTER_LIST,
  [ResourceSource.LOCAL_MUSIC]: QUERY_KEYS.LOCAL_MUSIC_LIST,
  [ResourceSource.LOCAL_SOUND]: QUERY_KEYS.LOCAL_SOUND_LIST,
  [ResourceSource.LOCAL_STICK_FOLDER]: QUERY_KEYS.LOCAL_PASTER_FOLDER_LIST,
  [ResourceSource.LOCAL_MUSIC_FOLDER]: QUERY_KEYS.LOCAL_MUSIC_FOLDER_LIST,
  [ResourceSource.LOCAL_SOUND_FOLDER]: QUERY_KEYS.LOCAL_SOUND_FOLDER_LIST,
}

/**
 * 操作对应的接口
 */
const resourceActions = {
  [ResourceSource.MEDIA]: {
    create: null,
    rename: (id: string, data: { title: string }) => ResourceModule.media.rename({ fileId: id, fileName: data.title }),
    delete: (id: string) => ResourceModule.media.delete({ fileIds: [id] }),
    move: (fileId: string, folderUuid: string) => ResourceModule.media.move({ fileIds: [fileId], folderUuid }),
    recycle: (fileId: string) => ResourceModule.media.recycle({ fileIds: [fileId] }),
  },
  [ResourceSource.FOLDER]: {
    create: (data: { title: string }, parentId: string) =>
      ResourceModule.directory.create({
        folderName: data.title,
        parentId: parentId || ' ',
      }),
    rename: (id: string, data: { title: string }) =>
      ResourceModule.directory.rename({ folderId: id, folderName: data.title }),
    delete: (id: string) => ResourceModule.directory.delete({ folderIds: [id] }),
    move: (folderId: string, parentId: string) => ResourceModule.directory.move({ folderIds: [folderId], parentId }),
    recycle: (folderId: string) => ResourceModule.directory.recycle({ folderIds: [folderId] }),
  },
  [ResourceSource.MULTI_SELECT]: {
    create: (data: { title: string }, parentId: string) =>
      ResourceModule.directory.create({
        folderName: data.title,
        parentId: parentId || ' ',
      }),
    rename: null,
    delete: null,
    move: null,
  },
  [ResourceSource.LOCAL_STICK_MULTI_SELECT]: {
    create: (data: { title: string }, parentId: string) =>
      ResourceModule.paster.dirCreate({
        folderName: data.title,
        parentId: parentId || ' ',
      }),
    rename: null,
    delete: null,
    move: (fileIds: string[], folderUuid: string) => ResourceModule.paster.localMove({ fileIds: fileIds, folderUuid }),
  },
  [ResourceSource.LOCAL_MUSIC_MULTI_SELECT]: {
    create: (data: { title: string }, parentId: string) =>
      ResourceModule.music.dirCreate({
        folderName: data.title,
        parentId: parentId || ' ',
      }),
    rename: null,
    delete: null,
    move: (fileIds: string[], folderUuid: string) => ResourceModule.music.localMove({ fileIds: fileIds, folderUuid }),
  },
  [ResourceSource.LOCAL_SOUND_MULTI_SELECT]: {
    create: (data: { title: string }, parentId: string) =>
      ResourceModule.voice.dirCreate({
        folderName: data.title,
        parentId: parentId || ' ',
      }),
    rename: null,
    delete: null,
    move: (fileIds: string[], folderUuid: string) => ResourceModule.voice.localMove({ fileIds: fileIds, folderUuid }),
  },
  [ResourceSource.LOCAL_STICK]: {
    create: null,
    rename: (id: string, data: { title: string }) =>
      ResourceModule.paster.localRename({ fileId: id, fileName: data.title }),
    delete: (id: string) => ResourceModule.paster.localDelete({ fileIds: [id] }),
    deleteMulit: (ids: string[]) => ResourceModule.paster.localDelete({ fileIds: ids }),
    move: (fileId: string, folderUuid: string) => ResourceModule.paster.localMove({ fileIds: [fileId], folderUuid }),
  },
  [ResourceSource.LOCAL_MUSIC]: {
    create: null,
    rename: (id: string, data: { title: string }) =>
      ResourceModule.music.localRename({ fileId: id, fileName: data.title }),
    delete: (id: string) => ResourceModule.music.localDelete({ fileIds: [id] }),
    deleteMulit: (ids: string[]) => ResourceModule.music.localDelete({ fileIds: ids }),
    move: (fileId: string, folderUuid: string) => ResourceModule.music.localMove({ fileIds: [fileId], folderUuid }),
  },
  [ResourceSource.LOCAL_SOUND]: {
    create: null,
    rename: (id: string, data: { title: string }) =>
      ResourceModule.voice.localRename({ fileId: id, fileName: data.title }),
    delete: (id: string) => ResourceModule.voice.localDelete({ fileIds: [id] }),
    deleteMulit: (ids: string[]) => ResourceModule.voice.localDelete({ fileIds: ids }),
    move: (fileId: string, folderUuid: string) => ResourceModule.voice.localMove({ fileIds: [fileId], folderUuid }),
  },
  [ResourceSource.LOCAL_STICK_FOLDER]: {
    create: (data: { title: string }, parentId: string) =>
      ResourceModule.paster.dirCreate({
        folderName: data.title,
        parentId: parentId || ' ',
      }),
    rename: (id: string, data: { title: string }) =>
      ResourceModule.paster.dirRename({ folderId: id, folderName: data.title }),
    delete: (id: string) => ResourceModule.paster.dirDelete({ folderIds: [id] }),
    move: (folderId: string, parentId: string) => ResourceModule.paster.dirMove({ folderIds: [folderId], parentId }),
  },
  [ResourceSource.LOCAL_MUSIC_FOLDER]: {
    create: (data: { title: string }, parentId: string) =>
      ResourceModule.music.dirCreate({
        folderName: data.title,
        parentId: parentId || ' ',
      }),
    rename: (id: string, data: { title: string }) =>
      ResourceModule.music.dirRename({ folderId: id, folderName: data.title }),
    delete: (id: string) => ResourceModule.music.dirDelete({ folderIds: [id] }),
    move: (folderId: string, parentId: string) => ResourceModule.music.dirMove({ folderIds: [folderId], parentId }),
  },
  [ResourceSource.LOCAL_SOUND_FOLDER]: {
    create: (data: { title: string }, parentId: string) =>
      ResourceModule.voice.dirCreate({
        folderName: data.title,
        parentId: parentId || ' ',
      }),
    rename: (id: string, data: { title: string }) =>
      ResourceModule.voice.dirRename({ folderId: id, folderName: data.title }),
    delete: (id: string) => ResourceModule.voice.dirDelete({ folderIds: [id] }),
    move: (folderId: string, parentId: string) => ResourceModule.voice.dirMove({ folderIds: [folderId], parentId }),
  },
} as const

/**
 * 文件夹：创建，重命名，删除，放入回收站
 * 媒体资源，我的音效，我的音乐，我的贴纸：重命名，删除
 */
export function useItemActions() {
  const queryClient = useQueryClient()
  const deleteModal = useDeleteModal()

  const executeAction = async (
    ResourceSource: ResourceSource,
    action: 'create' | 'rename' | 'delete' | 'move' | 'recycle' | 'deleteMulit',
    ...args: any[]
  ) => {
    const resourceAction = resourceActions[ResourceSource]
    if (!resourceAction || !resourceAction[action]) {
      throw new Error(`资源类型 ${ResourceSource} 不支持 ${action} 操作`)
    }
    return (resourceAction[action] as (...args: any[]) => Promise<any>)(...args)
  }

  const invalidate = async (type: ResourceSource) => {
    const queryKey = queryKeyMap[type]
    if (queryKey) {
      await queryClient.invalidateQueries({ queryKey: [queryKey] })
    }
  }

  const createItem = useCreateMaterial(async (type, parentId, data) => {
    await executeAction(type, 'create', data, parentId)
    await invalidate(type)
  })

  const renameItem = useRenameMaterial(async (type, id, _title, data) => {
    await executeAction(type, 'rename', id, data)
    await invalidate(type)
  })

  const deleteLocalItem = async (type: ResourceSource, nodeId: string, label: string) => {
    deleteModal({
      kind: '所选对象',
      name: label,
      danger: true,
      action: async () => {
        await executeAction(type, 'delete', nodeId)
        await invalidate(type)
      },
    })
  }

  const deleteLocalItemMulit = async (type: ResourceSource, nodeIds: string[], label: string) => {
    deleteModal({
      kind: '所选',
      name: label,
      danger: true,
      action: async () => {
        await executeAction(type, 'deleteMulit', nodeIds)
        await invalidate(type)
      },
    })
  }

  const deleteItem = async (type: ResourceSource, nodeId: string, label: string) => {
    deleteModal({
      kind: '所选对象',
      name: label,
      danger: true,
      buttons: ({ close }) => {
        const { pending, withPending } = usePending()

        return (
          <>
            <Button
              variant="default"
              className="min-w-[80px] h-8 ml-2 bg-white/5 text-white border hover:bg-primary-highlight1"
              onClick={withPending(async () => {
                await executeAction(type, 'recycle', nodeId)
                await invalidate(type)
                close()
              })}
            >
              {pending ? <Loader2 className="animate-spin size-4" /> : '放入回收站'}
            </Button>
            <Button
              variant="destructive"
              className="min-w-[80px] h-8 ml-2 bg-destructive text-white border hover:bg-destructive/90"
              onClick={withPending(async () => {
                await executeAction(type, 'delete', nodeId)
                await invalidate(type)
                close()
              })}
            >
              {pending ? <Loader2 className="animate-spin size-4" /> : '彻底删除'}
            </Button>
          </>
        )
      },
    })
  }

  const moveItem = async (type: ResourceSource, id: string, targetId: string) => {
    await executeAction(type, 'move', id, targetId)
    await invalidate(type)
  }

  const moveItemMulit = async (type: ResourceSource, ids: string[], targetId: string) => {
    await executeAction(type, 'move', ids, targetId)
    await invalidate(type)
  }

  return { createItem, renameItem, deleteLocalItem, deleteItem, deleteLocalItemMulit, moveItem, moveItemMulit, invalidate }
}
