import React from 'react'

export interface PendingHook {
  pending: boolean
  withPending: (fn: () => unknown) => () => Promise<void>
}

export function usePending(): PendingHook {
  const [pending, setPending] = React.useState(false)

  const withPending = React.useCallback((fn: () => unknown) => {
    return async () => {
      setPending(true)
      try {
        await fn()
      } finally {
        setPending(false)
      }
    }
  }, [])

  return { pending, withPending }
}
