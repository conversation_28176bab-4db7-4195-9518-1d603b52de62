export const QUERY_KEYS = {
  PASTER_LIST: 'PASTER_LIST',
  PASTER_CATEGORY: 'PASTER_CATEGORY',
  PASTER_COLLECTED: 'PASTER_COLLECTED',
  MUSIC_LIST: 'MUSIC_LIST',
  M<PERSON><PERSON>_CATEGORY: 'MUSIC_CATEGORY',
  MUSIC_COLLECTED: 'MUSIC_COLLECTED',
  MUSIC_RANK: 'MUSIC_RANK',
  VOICE_LIST: 'VOICE_LIST',
  VOICE_CATEGORY: 'VOICE_CATEGORY',
  VOICE_COLLECTED: 'VOICE_COLLECTED',
  FONT_LIST: 'FONT_LIST',
  FONT_CATEGORY: 'FONT_CATEGORY',
  FONT_COLLECTED: 'FONT_COLLECTED',
  BUBBLE_LETTERS: 'BUBBLE_LETTERS',
  FONT_STYLE_LIST: 'FONT_STYLE_LIST',
  FONT_STYLE_CATEGORY: 'FONT_STYLE_CATEGORY',
  TEXT_STYLE_LIST: 'TEXT_STYLE_LIST',
  TEXT_STYLE_CATEGORY: 'TEXT_STYLE_CATEGORY',
  RESOURCE_CACHE_STATUS: 'RESOURCE_CACHE_STATUS',
  RESOURCE_CACHE: 'RESOURCE_CACHE',
  RESOURCE_LOADING: 'RESOURCE_LOADING',
  RESOURCE_COLLECTION_STATUS: 'RESOURCE_COLLECTION_STATUS',
  RESOURCE_COLLECTED: 'RESOURCE_COLLECTED',
  RESOURCE_LIST: 'RESOURCE_LIST',
  PROJECT_LIST: 'PROJECT_LIST',
  PROJECT_PAGE: 'PROJECT_PAGE',
  SCRIPT_LIST: 'SCRIPT_LIST',
  SCRIPT_REMOVED_LIST: 'SCRIPT_REMOVED_LIST',
  SCRIPT_DETAIL: 'SCRIPT_DETAIL',
  WORK_LIST: 'WORK_LIST',
  SYSTEM_DICT: 'SYSTEM_DICT',
  CURRENT_FONT_PATH: 'CURRENT_FONT_PATH',
  MATERIAL_DIRECTORY_LIST: 'MATERIAL_DIRECTORY_LIST',
  MATERIAL_MEDIA_LIST: 'MATERIAL_MEDIA_LIST',
  ACCOUNT_GROUP: 'ACCOUNT_GROUP',
  AUTH_ACCOUNT_LIST: 'AUTH_ACCOUNT_LIST',
  GROUP_ACCOUNT_LIST: 'GROUP_ACCOUNT_LIST',
  ACCOUNT_PUSH_DETAIL: 'ACCOUNT_PUSH_DETAIL',
  AUTH_CODE: 'AUTH_CODE',
  DY_ACCOUNT_OVERVIEW: 'DY_ACCOUNT_OVERVIEW',
  DY_PUSH_PLAN_LIST: 'DY_PUSH_PLAN_LIST',
  MATRIX_DRAFT_LIST: 'MATRIX_DRAFT_LIST',
  MATRIX_DRAFT_DETAIL: 'MATRIX_DRAFT_DETAIL',
  MATRIX_PLAN_DETAIL: 'MATRIX_PLAN_DETAIL',
  MATERIAL_MEDIA_RECYCLR_LIST: 'MATERIAL_MEDIA_RECYCLR_LIST',
  MATERIAL_DIR_RECYCLR_LIST: 'MATERIAL_DIR_RECYCLR_LIST',
  REMIX_PREVIEW_PAGE: 'REMIX_PREVIEW_PAGE',
  VIDEO_LINK: 'VIDEO_LINK',
  SAVED_MIXCUT_LIST: 'SAVED_MIXCUT_LIST',
  LOCAL_PASTER_FOLDER_LIST: 'LOCAL_PASTER_FOLDER_LIST',
  LOCAL_MUSIC_FOLDER_LIST: 'LOCAL_MUSIC_FOLDER_LIST',
  LOCAL_SOUND_FOLDER_LIST: 'LOCAL_SOUND_FOLDER_LIST',
  LOCAL_PASTER_LIST: 'LOCAL_PASTER_LIST',
  LOCAL_MUSIC_LIST: 'LOCAL_MUSIC_LIST',
  LOCAL_SOUND_LIST: 'LOCAL_SOUND_LIST',
  PASTER_DIRECTORY_LIST: 'PASTER_DIRECTORY_LIST',
  PASTER_LOCAL_LIST: 'PASTER_LOCAL_LIST',
  TEAM_LIST: 'TEAM_LIST',
  TEAM_CURRENT: 'GROUP_CURRENT',
  TEAM_MEMBER_LIST: 'TEAM_MEMBER_LIST',
  TEAM_MEMBER: 'TEAM_MEMBER',
  TEAM_ROLES: 'TEAM_ROLES',
  TIMBRE_LIST: 'TIMBRE_LIST',
  COLLABORATOR_LIST: 'COLLABORATOR_LIST',
}
